<template>
  <div class="qr-code-form">
    <h1>{{ isEditMode ? 'QR 코드 수정' : 'QR 코드 생성' }}</h1>

    <div v-if="isLoading" class="loading">
      데이터를 불러오는 중...
    </div>

    <form v-else @submit.prevent="handleSubmit" class="form-container">
       <div class="form-group">
        <label for="qrName">QR 코드 이름 <span class="required">*</span></label>
        <input
          type="text"
          id="qrName"
          v-model="formData.qrName"
          required
          placeholder="QR 코드 이름을 입력하세요"
          :disabled="isEditMode"
        />
        <div v-if="isEditMode" class="field-note">수정 모드에서는 QR 코드 미리보기만 가능합니다.</div>
        
        <!-- 엑셀 파일 업로드 영역 (수정 모드가 아닐 때만 표시) -->
        <div v-if="!isEditMode" style="margin-top: 15px;">
          <button type="button" @click="downloadExcelTemplate">엑셀 템플릿 다운로드</button>
          <div style="margin-top: 10px;">
            <input type="file" @change="handleExcelFileSelected" accept=".xlsx,.xls,.csv" id="excelFileInput" />
          </div>
          <div v-if="excelFileName" style="margin-top: 10px;" class="excel-file-info">
            <div>선택된 파일: {{ excelFileName }}</div>
            <button type="button" class="delete-excel-btn" @click="removeExcelFile">삭제</button>
          </div>
          <p>* 한번에 최대 100개까지 등록 가능합니다.</p>
        </div>
      </div>
      <div class="form-group">
        <label for="qrType">QR 코드 타입 <span class="required">*</span></label>
        <select id="qrType" v-model="formData.qrType" :required="!excelFile" :disabled="isEditMode || excelFile">
          <option value="">{{ excelFile ? '엑셀 파일에서 등록됩니다' : '선택하세요' }}</option>
          <option value="URL">URL</option>
          <option value="TEXT">텍스트</option>
          <option value="SNS_LINK">SNS</option>
          <option value="WIFI">Wi-Fi</option>
          <option value="LOCATION">위치</option>
          <option value="LANDING_PAGE">랜딩페이지</option>
          <option value="EVENT_ATTENDANCE">이벤트</option>
        </select>
        <div v-if="isEditMode" class="field-note">QR 코드 타입은 생성 후 변경할 수 없습니다.</div>
      </div>

      <!-- 랜딩 페이지 선택 (QR 코드 타입이 랜딩페이지일 때만 표시) -->
      <div class="form-group" v-if="formData.qrType === 'LANDING_PAGE'">
        <label for="landingPageId">랜딩 페이지 선택 <span class="required">*</span></label>
        <div v-if="isLoadingLandingPages" class="loading-indicator">
          랜딩 페이지 목록을 불러오는 중...
        </div>
        <select
          v-else
          id="landingPageId"
          v-model="selectedLandingPageId"
          required
          :disabled="isEditMode"
        >
          <option value="">선택하세요</option>
          <option v-for="page in landingPages" :key="page.landingPageId || page.id" :value="page.landingPageId || page.id">
            {{ page.pageTitle || page.title || page.name || '랜딩 페이지 ' + (page.landingPageId || page.id) }}
          </option>
        </select>
        <div v-if="landingPages.length === 0 && !isLoadingLandingPages" class="field-note error">
          사용 가능한 랜딩 페이지가 없습니다. 먼저 랜딩 페이지를 생성해주세요.
        </div>
      </div>

      <!-- 이벤트 선택 (QR 코드 타입이 이벤트일 때만 표시) -->
      <div class="form-group" v-if="formData.qrType === 'EVENT_ATTENDANCE'">
        <label for="eventId">이벤트 선택 <span class="required">*</span></label>
        <div v-if="isLoadingEvents" class="loading-indicator">이벤트 목록을 불러오는 중...</div>
        <select v-else id="eventId" v-model="selectedEventId" required>
          <option value="">선택하세요</option>
          <option v-for="eventItem in events" :key="eventItem.eventId" :value="eventItem.eventId">
            {{ eventItem.eventName }}
          </option>
        </select>
        <div v-if="events.length === 0 && !isLoadingEvents" class="field-note error">
          사용 가능한 이벤트가 없습니다. 먼저 이벤트를 생성해주세요.
        </div>
      </div>

      <!-- 위치 타입일 때 위치 선택 UI -->
      <div class="form-group" v-if="formData.qrType === 'LOCATION'">
        <label for="locationTargetContent">위치 정보 (QR 코드 내용) <span class="required">*</span></label>
        <div class="location-target-inputs">
          <div class="location-input-group">
            <label for="targetLatitude">위도</label>
            <input
              type="text"
              id="targetLatitude"
              v-model="targetLocationData.latitude"
              placeholder="위도 (예: 37.5665)"
              required
              :disabled="isEditMode"
            />
          </div>
          <div class="location-input-group">
            <label for="targetLongitude">경도</label>
            <input
              type="text"
              id="targetLongitude"
              v-model="targetLocationData.longitude"
              placeholder="경도 (예: 126.9780)"
              required
              :disabled="isEditMode"
            />
          </div>
        </div>
        <div class="location-address">
          <label for="targetLocationAddress">주소</label>
          <input
            type="text"
            id="targetLocationAddress"
            v-model="targetLocationData.address"
            placeholder="주소 정보"
            readonly
            :disabled="isEditMode"
          />
        </div>
        <button
          type="button"
          class="map-select-button location-target-button"
          @click="openTargetLocationMap"
          :disabled="isEditMode"
        >
          카카오맵에서 위치 선택
        </button>
        <div class="field-note">
          QR 코드에 스캔시 보여질 위치 정보입니다. 위도와 경도를 직접 입력하거나 카카오맵에서 위치를 선택할 수 있습니다.
        </div>
        <div class="target-content-preview" v-if="formData.targetContent">
          <label>타겟 콘텐츠 미리보기:</label>
          <div class="preview-content">{{ formData.targetContent }}</div>
        </div>
      </div>

      <!-- Wi-Fi 타입일 때 Wi-Fi 정보 입력 UI -->
      <div class="form-group" v-if="formData.qrType === 'WIFI'">
        <label for="wifiInfo">Wi-Fi 정보 (QR 코드 내용) <span class="required">*</span></label>
        <div class="wifi-inputs">
          <div class="wifi-input-group">
            <label for="wifiSsid">네트워크 이름 (SSID) <span class="required">*</span></label>
            <input
              type="text"
              id="wifiSsid"
              v-model="wifiData.ssid"
              placeholder="Wi-Fi 네트워크 이름"
              required
            />
          </div>

          <div class="wifi-input-group">
            <label for="wifiSecurityType">보안 유형 <span class="required">*</span></label>
            <select id="wifiSecurityType" v-model="wifiData.securityType">
              <option value="WPA">WPA/WPA2/WPA3</option>
              <option value="WEP">WEP</option>
              <option value="">보안 없음</option>
            </select>
          </div>

          <div class="wifi-input-group" v-if="wifiData.securityType">
            <label for="wifiPassword">비밀번호 <span class="required">*</span></label>
            <div class="password-input-container">
              <input
                :type="showPassword ? 'text' : 'password'"
                id="wifiPassword"
                v-model="wifiData.password"
                placeholder="Wi-Fi 비밀번호"
                required
              />
              <button
                type="button"
                class="toggle-password-button"
                @click="togglePasswordVisibility"
              >
                {{ showPassword ? '숨기기' : '보기' }}
              </button>
            </div>
          </div>

          <div class="wifi-checkbox-group">
            <input
              type="checkbox"
              id="wifiHidden"
              v-model="wifiData.hidden"
            />
            <label for="wifiHidden">숨겨진 네트워크</label>
          </div>
        </div>
        <div class="field-note">
          QR 코드를 스캔하면 자동으로 Wi-Fi에 연결됩니다. 정확한 정보를 입력해주세요.
        </div>
        <div class="target-content-preview" v-if="formData.targetContent">
          <label>타겟 콘텐츠 미리보기:</label>
          <div class="preview-content">{{ formData.targetContent }}</div>
        </div>
      </div>

      <!-- 타겟 콘텐츠 (QR 코드 타입이 랜딩페이지, 이벤트, 위치, Wi-Fi가 아닐 때만 표시) -->
      <div class="form-group" v-if="formData.qrType !== 'LANDING_PAGE' && formData.qrType !== 'EVENT_ATTENDANCE' && formData.qrType !== 'LOCATION' && formData.qrType !== 'WIFI'">
        <label for="targetContent">타겟 콘텐츠 <span class="required">*</span></label>
        <textarea
          id="targetContent"
          v-model="formData.targetContent"
          :required="!excelFile"
          :placeholder="excelFile ? '엑셀 파일에서 등록됩니다' : 'QR 코드에 포함될 내용을 입력하세요'"
          rows="4"
          :disabled="excelFile"
          :class="{ 'input-error': (formData.qrType === 'URL' || formData.qrType === 'SNS_LINK') && formData.targetContent && !isValidTargetContent }"
        ></textarea>
        <div v-if="(formData.qrType === 'URL' || formData.qrType === 'SNS_LINK') && formData.targetContent && !isValidTargetContent" class="error-message">
          유효한 URL 형식이 아닙니다. (예: https://example.com)
        </div>
        <div class="field-note" v-if="formData.qrType === 'URL'">
          http:// 또는 https:// 를 포함한 전체 URL을 입력해주세요.
        </div>
        <div class="field-note" v-if="formData.qrType === 'SNS_LINK'">
          연결할 SNS 페이지의 전체 URL을 입력해주세요. (예: https://www.instagram.com/username)
        </div>
        <div class="field-note" v-if="formData.qrType === 'TEXT'">
          QR 코드에 포함될 텍스트를 입력해주세요.
        </div>
      </div>

      <div class="form-group">
        <label for="status">상태 <span class="required">*</span></label>
        <select id="status" v-model="formData.status" required>
          <option value="">선택하세요</option>
          <option value="ACTIVE">활성</option>
          <option value="INACTIVE">비활성</option>
        </select>
      </div>

      <div class="form-group date-group">
        <label for="validFromDate">유효 시작일</label>
        <input
          type="datetime-local"
          id="validFromDate"
          v-model="formData.validFromDate"
        />
      </div>

      <div class="form-group date-group">
        <label for="validToDate">유효 종료일</label>
        <input
          type="datetime-local"
          id="validToDate"
          v-model="formData.validToDate"
        />
      </div>

      <div class="form-group">
        <label for="description">설명</label>
        <textarea
          id="description"
          v-model="formData.description"
          placeholder="QR 코드에 대한 설명을 입력하세요"
          rows="3"
        ></textarea>
      </div>

      <!-- QR 코드 설치 위치 입력 -->
      <div class="form-group">
        <label for="locationInfo">QR 코드 설치 위치</label>
        <div class="location-inputs">
          <div class="location-input-group">
            <label for="latitude">위도</label>
            <input
              type="text"
              id="latitude"
              v-model="locationData.latitude"
              placeholder="위도 (예: 37.5665)"
            />
          </div>
          <div class="location-input-group">
            <label for="longitude">경도</label>
            <input
              type="text"
              id="longitude"
              v-model="locationData.longitude"
              placeholder="경도 (예: 126.9780)"
            />
          </div>
        </div>
        <div class="location-address">
          <label for="locationAddress">주소</label>
          <input
            type="text"
            id="locationAddress"
            v-model="locationData.address"
            placeholder="주소 정보"
          />
        </div>
        <button
          type="button"
          class="map-select-button"
          @click="openKakaoMap"
        >
          카카오맵에서 위치 선택
        </button>
        <div class="field-note">
          QR 코드가 설치될 위치의 위도와 경도를 직접 입력하거나 카카오맵에서 위치를 선택할 수 있습니다.
        </div>
        
        <!-- QR 코드 설치 위치 사진 업로드 -->
        <div class="location-image-upload">
          <h4>QR 코드 설치 사진</h4>
          <p class="field-note">설치 위치의 사진을 업로드해주세요. (최대 크기: 5MB)</p>
          
          <div class="location-image-preview-container">
            <!-- 이미지 미리보기 -->
            <div v-if="locationImagePreview" class="location-image-preview">
              <img :src="locationImagePreview" alt="Location Image Preview" class="location-image" @error="handleImageError" />
              <button type="button" @click="removeLocationImage" class="remove-image-btn">삭제</button>
            </div>
            
            <!-- 이미지 업로드 버튼 -->
            <div v-else class="location-image-upload-button">
              <label @click="prepareImageUpload" class="location-image-upload-label">
                <span class="upload-icon">+</span>
                <span>설치 위치 사진 선택</span>
              </label>
              <input
                v-if="showFileInput"
                :key="fileInputKey"
                type="file"
                id="locationImageFile"
                ref="locationImageInput"
                @change="handleLocationImageUpload"
                accept="image/png,image/jpeg,image/jpg"
                class="location-image-file-input"
                style="display: none;"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="form-group" v-if="!isEditMode">
        <label>QR 코드 디자인 옵션</label>
        <div class="design-options-container">
          <!-- 로고 업로드 섹션 -->
          <div class="logo-upload-section">
            <h4>로고 이미지 업로드</h4>
            <p class="field-note">QR 코드 중앙에 표시될 로고 이미지를 업로드하세요. (최대 크기: 1MB)</p>

            <div class="logo-preview-container">
              <!-- 로고 미리보기 -->
              <div v-if="logoPreview" class="logo-preview">
                <img :src="logoPreview" alt="Logo Preview" class="logo-image" @error="handleImageError" />
                <button type="button" @click="removeLogo" class="remove-logo-btn" :disabled="isEditMode">삭제</button>
              </div>

              <!-- 로고 업로드 버튼 -->
              <div v-if="!logoPreview" class="logo-upload">
                <label for="logoFile" class="logo-upload-label">
                  <span class="upload-icon">+</span>
                  <span>로고 이미지 선택</span>
                </label>
                <input
                  type="file"
                  id="logoFile"
                  ref="logoInput"
                  @change="handleLogoUpload"
                  accept="image/png,image/jpeg,image/svg+xml"
                  class="logo-file-input"
                  :disabled="isEditMode"
                />
              </div>
            </div>

            <div class="logo-options">
              <div class="logo-size-option">
                <!-- [수정] 레이블 텍스트 변경 -->
                <label for="logoSize">로고 크기 (QR 코드 영역 대비 %)</label>
                <input
                  type="range"
                  id="logoSize"
                  v-model="logoSize"
                  min="10"
                  max="40"
                  step="5"
                  :disabled="!logoPreview || isEditMode"
                />
                <!-- [수정] 설명 텍스트 변경 -->
                <div class="field-note">최대 40%까지 적용됩니다 (스캔율 저하 방지).</div>
                <div class="field-note" style="margin-top: 2px; color: #888;">
                  참고: 오류 복원 수준 변경 시 QR 코드 패턴 밀도가 달라져 로고의 시각적 크기가 다르게 보일 수 있습니다.
                </div>
              </div>
            </div>
          </div>

          <!-- 고급 디자인 옵션 -->
          <div class="advanced-options">
            <h4>고급 디자인 옵션</h4>
            <div class="form-group design-group">
              <label for="qrColor">QR 코드 색상</label>
              <input type="color" id="qrColor" v-model="qrColor" :disabled="isEditMode" />
            </div>
            <div class="form-group design-group">
              <label for="qrBgColor">배경 색상</label>
              <input type="color" id="qrBgColor" v-model="qrBgColor" :disabled="isEditMode" />
            </div>
            <div class="form-group design-group">
              <label for="qrEyeColor">눈 색상</label>
              <input type="color" id="qrEyeColor" v-model="qrEyeColor" :disabled="isEditMode" />
            </div>
            <div class="form-group design-group slider-group">
              <label for="qrEyeStyle">눈 모양 (네모 → 동그라미)</label>
              <input type="range" id="qrEyeStyle" v-model.number="qrEyeStyle" min="0" max="1" step="1" :disabled="isEditMode" />
              <span>{{ qrEyeStyle === 0 ? '네모' : '동그라미' }}</span>
            </div>

            <!-- 오류 복원 수준 선택 -->
            <div class="form-group design-group">
              <label for="qrErrorCorrectionLevel">오류 복원 수준</label>
              <select id="qrErrorCorrectionLevel" v-model="qrErrorCorrectionLevel" class="error-correction-select" :disabled="isEditMode">
                <option value="L">낮음 (L: ~7% 복원)</option>
                <option value="M">중간 (M: ~15% 복원)</option>
                <option value="Q">높음 (Q: ~25% 복원)</option>
                <option value="H">매우 높음 (H: ~30% 복원)</option>
              </select>
              <div class="field-note">
                오류 복원 수준이 높을수록 QR 코드 스캔이 잘 됩니다.
              </div>
            </div>

            <!-- 캔버스 박스 사용 토글 -->
            <div class="form-group design-group">
              <div class="toggle-container">
                <label for="useA4Canvas">캔버스 박스 사용</label>
                <div class="toggle-switch">
                  <input type="checkbox" id="useA4Canvas" v-model="useA4Canvas" :disabled="isEditMode" />
                  <label for="useA4Canvas"></label>
                </div>
              </div>
              <div class="field-note">
                자유롭게 크기를 조절할 수 있는 캔버스 박스를 사용하여 QR코드와 배경 이미지 위치를 정확히 설정할 수 있습니다.
              </div>
            </div>

            <!-- 배경 이미지 추가 토글 (A4 박스 사용 시에만 표시) -->
            <div class="form-group design-group" v-if="useA4Canvas">
              <div class="toggle-container">
                <label for="useBackgroundImage">배경 이미지 추가</label>
                <div class="toggle-switch">
                  <input type="checkbox" id="useBackgroundImage" v-model="useBackgroundImage" :disabled="isEditMode" />
                  <label for="useBackgroundImage"></label>
                </div>
              </div>
              <div v-if="useBackgroundImage" class="background-image-container">
                <div class="background-image-controls">
                  <div v-if="backgroundImagePreview" class="bg-image-info">
                    <span class="bg-image-name">이미지: {{ backgroundImageFile ? backgroundImageFile.name : '배경 이미지' }}</span>
                    <button type="button" class="secondary-btn remove-bg-btn-outside" @click="removeBackgroundImage" :disabled="isEditMode">배경 제거</button>
                  </div>
                  <div v-else class="bg-upload-outside">
                    <label for="backgroundFile" class="background-upload-btn">
                      <span class="upload-icon">+</span>
                      <span>배경 이미지 선택</span>
                    </label>
                    <input
                      type="file"
                      id="backgroundFile"
                      ref="backgroundInput"
                      @change="handleBackgroundUpload"
                      accept="image/png,image/jpeg,image/svg+xml"
                      class="background-file-input"
                      :disabled="isEditMode"
                    />
                  </div>

                  <div v-if="backgroundImagePreview" class="canvas-toolbar">
                    <span>이미지 조절:</span>
                    <div class="image-controls">
                      <button
                        type="button"
                        class="control-btn"
                        :class="{active: backgroundFitMode === 'fill'}"
                        @click="setBackgroundFitMode('fill')"
                      >
                        채우기
                      </button>
                      <button
                        type="button"
                        class="control-btn"
                        :class="{active: backgroundFitMode === 'fit'}"
                        @click="setBackgroundFitMode('fit')"
                      >
                        맞추기
                      </button>
                      <button
                        type="button"
                        class="control-btn"
                        :class="{active: backgroundFitMode === 'original'}"
                        @click="setBackgroundFitMode('original')"
                      >
                        원본
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 캔버스 박스 (A4 박스 사용 시에만 표시) -->
            <div v-if="useA4Canvas" class="form-group a4-canvas-section">
              <h4>캔버스 박스</h4>
              <div class="a4-canvas">
                <!-- A4 캔버스 부모 컨테이너 -->
                <div class="a4-canvas-parent-container">
                  <!-- A4 캔버스 (크기 조절만 가능) -->
                  <vue-draggable-resizable
                    :parent="true"
                    :x="a4CanvasX"
                    :y="a4CanvasY"
                    :w="a4CanvasWidth"
                    :h="a4CanvasHeight"
                    @resize="onA4CanvasResize"
                    @resizestop="onA4CanvasResize"
                    @drag="onA4CanvasDrag"
                    @dragstop="onA4CanvasDrag"
                    :min-width="200"
                    :min-height="200"
                    :max-width="1000"
                    :max-height="1400"
                    :grid="[1, 1]"
                    :handles="['tm', 'mr', 'bm', 'ml']"
                    :lock-aspect-ratio="false"
                    :draggable="false"
                    :resizable="true"
                    :prevent-deactivation="true"
                    class="a4-canvas-draggable-container"
                  >
                  <div class="canvas-container" ref="a4CanvasContainer">
                    <!-- 배경 이미지 (크기 조절 가능) -->
                    <div v-if="useBackgroundImage && backgroundImagePreview" class="background-container-wrapper">
                      <vue-draggable-resizable
                        :parent="true"
                        :x="backgroundPositionX"
                        :y="backgroundPositionY"
                        :w="backgroundWidth"
                        :h="backgroundHeight"
                        @drag="onBackgroundDrag"
                        @resize="onBackgroundResize"
                        @dragstop="onBackgroundDrag"
                        @resizestop="onBackgroundResize"
                        :min-width="100"
                        :min-height="100"
                        :grid="[1, 1]"
                        :handles="['tl', 'tr', 'bl', 'br']"
                        class="background-draggable-container"
                      >
                        <div class="background-image-preview">
                          <img
                            :src="backgroundImagePreview"
                            class="background-preview"
                            :class="[`bg-${backgroundFitMode}`]"
                          />
                        </div>
                      </vue-draggable-resizable>
                    </div>

                    <!-- QR 코드 (크기 조절 가능) -->
                    <div class="qr-container-wrapper" v-if="formData && (formData.generatedQrDataUrl || formData.imageUrl || (formData.targetContent && formData.targetContent.trim() !== '')) && !imageLoadError">
                      <vue-draggable-resizable
                        :parent="true"
                        :x="qrPositionX"
                        :y="qrPositionY"
                        :w="qrWidth"
                        :h="qrHeight"
                        @drag="onDragWithBounds"
                        @resize="onResizeWithAspectRatio"
                        @dragstop="onDragWithBounds"
                        @resizestop="onResizeWithAspectRatio"
                        :min-width="50"
                        :min-height="50"
                        :max-width="200"
                        :max-height="200"
                        :grid="[1, 1]"
                        :handles="['tl', 'tr', 'bl', 'br']"
                        :lock-aspect-ratio="true"
                        :aspect-ratio="1"
                        class="qr-draggable-container"
                      >
                        <div class="qr-on-canvas">
                          <img
                            v-if="formData.generatedQrDataUrl || formData.imageUrl"
                            :src="formData.generatedQrDataUrl || formData.imageUrl"
                            alt="QR Code on Canvas"
                            class="qr-image-on-canvas"
                          />
                          <div v-else class="qr-placeholder-on-canvas">
                            QR 코드
                          </div>
                        </div>
                      </vue-draggable-resizable>
                    </div>
                    </div>
                  </vue-draggable-resizable>
                </div>
              </div>
              <div class="field-note">
                캔버스 박스의 크기를 자유롭게 조절할 수 있습니다. 상하좌우 선 가운데의 핸들을 드래그하여 각 방향별로 독립적으로 크기를 조절하세요.
                <br>• 상단/하단 핸들: 높이 조절
                <br>• 좌측/우측 핸들: 너비 조절
                 <br>캔버스 크기: {{ mmA4CanvasWidth }}mm × {{ mmA4CanvasHeight }}mm
                <br>QR코드 위치: X={{ mmPositionX }}mm, Y={{ mmPositionY }}mm (크기: {{ mmWidth }}mm × {{ mmHeight }}mm)
                <span v-if="useBackgroundImage && backgroundImagePreview">
                  <br>배경 이미지 위치: X={{ mmBackgroundPositionX }}mm, Y={{ mmBackgroundPositionY }}mm (크기: {{ mmBackgroundWidth }}mm × {{ mmBackgroundHeight }}mm)
                </span>
              </div>
            </div>
            <button type="button" @click="resetDesignOptions" class="btn btn-secondary btn-sm reset-btn" :disabled="isEditMode">스타일 초기화</button>
          </div>
        </div>
      </div>


      <!-- QR 코드 미리보기 섹션 -->
      <div class="qr-code-preview">
        <h3>QR 코드 미리보기</h3>
        <div v-if="isEditMode" class="edit-mode-notice">
          <strong>수정 모드에서는 QR 코드 미리보기만 가능합니다.</strong>
        </div>
        <!-- 클라이언트에서 생성한 QR 코드 미리보기 -->
        <div ref="qrCodePreviewContainer" class="qr-preview-container">
          <img
          v-if="formData && formData.imageUrl && !imageLoadError"
          :src="formData.imageUrl"
          alt="QR Code Image"
          class="qr-image"
          @error="handleImageError"
        />
        </div>
        <div v-if="!formData.targetContent" class="image-placeholder">
          타겟 콘텐츠를 입력하면 QR 코드가 보입니다.<br>(샘플 이미지이며 생성 요청시 실제 QR코드가 생성됩니다.)
        </div>

        <!-- QR 코드 버전 및 크기 정보 (수정 모드에서는 표시하지 않음) -->
        <div v-if="formData.targetContent && !isEditMode" class="qr-version-info">
          <!-- <div class="version-label">
            <span>QR 코드 버전:</span>
            <span class="version-value">{{ qrVersion > 0 ? qrVersion : '자동' }}</span>
            <span class="size-info">(250x250 px)</span>
          </div> -->
          <div class="version-note">
            위의 QR코드는 샘플이며 등록시 디자인을 반영한 실제 QR코드가 생성됩니다.
          </div>
        </div>

        <!-- QR 코드 스캔율 표시 (수정 모드에서는 표시하지 않음) -->
        <div v-if="(formData.targetContent || excelSampleQrData.isActive) && !isEditMode" class="scan-reliability-container">
          <div class="scan-reliability-label">
            <span>스캔율:</span>
            <span :class="getScanReliabilityClass">{{ scanReliability }}%</span>
          </div>
          <div class="scan-reliability-progress-container">
            <div class="scan-reliability-progress" :style="{ width: `${scanReliability}%`, backgroundColor: getScanReliabilityColor }"></div>
          </div>
          <div class="scan-reliability-note">
            {{ getScanReliabilityMessage }}
          </div>
        </div>
      </div>



      <div v-if="error" class="error-message form-error">
        {{ error }}
      </div>

      <div class="form-actions">
        <button type="button" @click="goBack" class="cancel-btn">취소</button>
        <button type="submit" class="submit-btn" :disabled="isSubmitting">
          {{ isSubmitting ? '처리 중...' : (isEditMode ? '수정' : '생성') }}
        </button>
      </div>
      
      <!-- 연속 생성 진행 상황 및 결과 표시 영역 -->
      <div v-if="Array.isArray(qrBatchCreationList) && qrBatchCreationList.length > 0" class="qr-batch-results">
        <h3>QR 코드 연속 생성 결과</h3>
        
        <!-- 전체 진행률 표시 -->
        <div v-if="isBatchProcessing" class="batch-progress">
          <div class="progress-text">
            {{Math.floor(batchProgress)}}% 완료 ({{Array.isArray(qrBatchCreationList) ? qrBatchCreationList.filter(item => item.status === 'success' || item.status === 'error').length : 0}}/{{Array.isArray(qrBatchCreationList) ? qrBatchCreationList.length : 0}}개)
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar" :style="{width: batchProgress + '%'}"></div>
          </div>
        </div>
        
        <!-- 전역 오류 메시지 -->
        <div v-if="batchGlobalError" class="batch-error-message">
          오류: {{ batchGlobalError }}
        </div>
        
        <!-- 결과 요약 (완료 후) -->
        <div v-if="!isBatchProcessing && Array.isArray(qrBatchCreationList) && qrBatchCreationList.length > 0" class="batch-summary">
          <p>처리 완료: 총 {{Array.isArray(qrBatchCreationList) ? qrBatchCreationList.length : 0}}개 중 
            <span class="success-count">{{Array.isArray(qrBatchCreationList) ? qrBatchCreationList.filter(item => item.status === 'success').length : 0}}개 성공</span>, 
            <span class="error-count">{{Array.isArray(qrBatchCreationList) ? qrBatchCreationList.filter(item => item.status === 'error').length : 0}}개 실패</span>
          </p>
        </div>
        
        <!-- 개별 QR 코드 생성 결과 목록 -->
        <div class="batch-results-list">
          <table class="batch-results-table">
            <thead>
              <tr>
                <th>행 번호</th>
                <th>QR 코드 이름</th>
                <th>타입</th>
                <th>타겟 콘텐츠</th>
                <th>상태</th>
                <th>메시지</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in (Array.isArray(qrBatchCreationList) ? qrBatchCreationList : [])" :key="item.id" :class="{'row-success': item.status === 'success', 'row-error': item.status === 'error'}">
                <td>{{item.excelRowNumber}}</td>
                <td>{{item.finalQrName}}</td>
                <td>{{item.qrType}}</td>
                <td>{{item.targetContent && item.targetContent.length > 30 ? item.targetContent.substring(0, 30) + '...' : item.targetContent}}</td>
                <td>
                  <span v-if="item.status === 'pending'" class="status-pending">대기 중...</span>
                  <span v-else-if="item.status === 'processing'" class="status-processing">생성 중...</span>
                  <span v-else-if="item.status === 'success'" class="status-success">성공</span>
                  <span v-else-if="item.status === 'error'" class="status-error">실패</span>
                </td>
                <td>
                  <span v-if="item.status === 'error'" class="error-message">{{item.errorMessage}}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </form>
    
    <!-- QR 코드 설치 위치 선택용 카카오맵 모달 -->
    <KakaoMapModal
      :is-visible="showKakaoMapModal"
      :initial-location="locationData.latitude && locationData.longitude ? {
        lat: parseFloat(locationData.latitude),
        lng: parseFloat(locationData.longitude),
        address: locationData.address
      } : null"
      @close="showKakaoMapModal = false"
      @select-location="handleLocationSelect"
    />

    <!-- QR 코드 타겟 위치 선택용 카카오맵 모달 -->
    <KakaoMapModal
      :is-visible="showTargetLocationMapModal"
      :initial-location="targetLocationData.latitude && targetLocationData.longitude ? {
        lat: parseFloat(targetLocationData.latitude),
        lng: parseFloat(targetLocationData.longitude),
        address: targetLocationData.address
      } : null"
      @close="showTargetLocationMapModal = false"
      @select-location="handleTargetLocationSelect"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, nextTick } from 'vue';
import VueDraggableResizable from 'vue3-draggable-resizable';
import 'vue3-draggable-resizable/dist/Vue3DraggableResizable.css';
import * as XLSX from 'xlsx';
import { useRouter, useRoute } from 'vue-router';
import { getQrCodeById, createQrCode, updateQrCode } from '@/api/qrcode';
import QRCodeStyling from 'qr-code-styling'; // 새 라이브러리 import
import { useAuthStore } from '@/stores/auth';
import { formatISO, parseISO } from 'date-fns';
import { getLandingPages } from '@/api/landing';
import { getEvents } from '@/api/events';
import KakaoMapModal from '@/components/map/KakaoMapModal.vue';
import { useImageError } from '@/composables/useImageError';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const isLoading = ref(true);
const error = ref(null);
const isSubmitting = ref(false);
const qrCode = ref(null); // QR 코드 데이터 저장용 변수 추가
const imageLoadError = ref(false); // 이미지 로드 오류 상태
const logoPreview = ref(null); // 로고 이미지 미리보기 URL
const logoSize = ref(40); // 로고 크기 (기본값: 원본 이미지 크기의 40%)
const originalLogoSize = ref({ width: 0, height: 0 }); // 원본 로고 이미지 크기
const logoInput = ref(null); // 로고 파일 인풋 엘리먼트 참조
const logoFile = ref(null); // 업로드된 로고 파일
const qrCodeInstance = ref(null);
const qrCodePreviewContainer = ref(null); // DOM 요소 참조 추가

// 랜딩 페이지 관련 상태
const landingPages = ref([]);
const isLoadingLandingPages = ref(false);
const selectedLandingPageId = ref('');

// 이벤트 관련 상태
const events = ref([]);
const isLoadingEvents = ref(false);
const selectedEventId = ref('');
// 이미지 에러 핸들링
const { handleImageError } = useImageError();

// QR 코드 설치 위치 관련 상태
const locationData = ref({
  latitude: '',
  longitude: '',
  address: ''
});
const locationImageFile = ref(null); // 위치 이미지 파일
const locationImagePreview = ref(''); // 이미지 미리보기 URL
const locationImageInput = ref(null); // 이미지 파일 입력 요소 참조
const fileInputKey = ref(0); // 파일 입력 엘리먼트를 강제로 다시 마운트하기 위한 키
const showFileInput = ref(true); // 파일 입력 엘리먼트 표시 여부
const showKakaoMapModal = ref(false);

// QR 코드 타겟 위치 관련 상태
const targetLocationData = ref({
  latitude: '',
  longitude: '',
  address: ''
});
const showTargetLocationMapModal = ref(false);

// QR 코드 타입이 'WIFI'일 때 Wi-Fi 정보 상태
const wifiData = ref({
  ssid: '',
  securityType: 'WPA', // 기본값: WPA/WPA2/WPA3
  password: '',
  hidden: false
});
const showPassword = ref(false); // 비밀번호 표시 여부

// QR 코드 색상 옵션
const qrColor = ref('#000000'); // QR 코드 색상
const qrBgColor = ref('#FFFFFF'); // 배경 색상
const qrEyeColor = ref('#000000'); // 눈 색상
const qrEyeStyle = ref(0); // 0: 네모, 1: 동그라미
const qrDotsStyle = ref(0); // 0: 네모, 1: 동그라미

// A4 캔버스 및 배경 이미지 관련 변수 추가
const useA4Canvas = ref(false); // A4 용지 박스 사용 여부
const useBackgroundImage = ref(false); // 배경 이미지 사용 여부

// 디자인옵션 객체 초기화 (엑셀 업로드 기능에서 사용하기 위해 명시적으로 초기화)
const designOptions = ref({
  dotsOptions: {
    color: '#000000',
    type: 'square'
  },
  cornersSquareOptions: {
    color: '#000000',
    type: 'square'
  },
  cornersDotOptions: {
    color: '#000000',
    type: 'square'
  },
  backgroundOptions: {
    color: '#FFFFFF',
  },
  imageOptions: {
    hideBackgroundDots: true,
    imageSize: 0.4,
    margin: 0
  }
});

// QR 코드 오류 복원 수준 (Error Correction Level)
// L: 데이터의 ~7% 복원 가능, M: ~15%, Q: ~25%, H: ~30%
const qrErrorCorrectionLevel = ref('M'); // 기본값: M (Medium)

// QR 코드 버전 (1~40, 가능한 경우 자동 계산)
const qrVersion = ref(0); // 0: 자동 계산

// 데이터 길이와 오류 복원 수준에 따라 QR 코드 버전 계산
const calculateQrVersion = (content, errorCorrectionLevel) => {
  if (!content) return 1; // 데이터가 없으면 최소 버전

  const contentLength = content.length;

  // 오류 복원 수준에 따른 버전 계수 조정 계수
  // M과 Q 사이의 차이를 더 크게 만들기 위해 계수 조정
  let levelFactor;
  switch (errorCorrectionLevel) {
    case 'L': levelFactor = 1.0; break;  // 기본 버전
    case 'M': levelFactor = 1.2; break;  // L보다 20% 더 크게
    case 'Q': levelFactor = 2.0; break;  // L보다 100% 더 크게 (M과의 차이를 크게 만들기 위해 증가)
    case 'H': levelFactor = 3.0; break;  // L보다 200% 더 크게
    default: levelFactor = 1.2; // 기본값 M
  }

  // 데이터 길이에 따른 기본 버전 계산 (더 명확한 차이를 위해 수정)
  let baseVersion;
  if (contentLength <= 25) {
    baseVersion = 2; // 최소 버전을 2로 설정하여 더 명확한 차이 제공
  } else if (contentLength <= 50) {
    baseVersion = 3;
  } else if (contentLength <= 100) {
    baseVersion = 4;
  } else if (contentLength <= 150) {
    baseVersion = 5;
  } else if (contentLength <= 200) {
    baseVersion = 7;
  } else if (contentLength <= 250) {
    baseVersion = 9;
  } else if (contentLength <= 300) {
    baseVersion = 12;
  } else {
    baseVersion = Math.min(40, Math.ceil(contentLength / 25)); // 최대 버전 40, 더 빠르게 버전 증가
  }

  // 최소 버전을 2로 설정하여 더 명확한 차이 제공
  baseVersion = Math.max(2, baseVersion);

  // 오류 복원 수준을 고려한 최종 버전 계산
  const finalVersion = Math.min(40, Math.ceil(baseVersion * levelFactor));

  return finalVersion;
};

// QR 코드 스캔율 관련 상태
const scanReliability = ref(95); // 기본 스캔율 (퍼센트)

// 폼 데이터 초기화
const formData = ref({
  qrName: '',
  qrType: '',
  targetContent: '',
  status: 'ACTIVE',
  linkedEventId: '',
  description: '',
  validFromDate: '',
  validToDate: '',

  // designOptions 는 이제 스크립트에서 동적으로 생성/파싱합니다.
});

// 현재 선택된 프로젝트
const currentProject = computed(() => {
  return authStore.currentProject;
});

// 수정 모드 여부
const isEditMode = computed(() => {
  return !!route.params.qrCodeId;
});

// URL 유효성 검사 함수
const isValidUrl = (url) => {
  return url && (url.startsWith('http://') || url.startsWith('https://'));
};

// URL 타입일 때 타겟 콘텐츠 유효성 검사
const isValidTargetContent = computed(() => {
  if (formData.value.qrType === 'URL' || formData.value.qrType === 'SNS_LINK') {
    return isValidUrl(formData.value.targetContent);
  }
  return true;
});

// URL 타입일 때 타겟 콘텐츠 오류 메시지
const targetContentErrorMessage = computed(() => {
  if ((formData.value.qrType === 'URL' || formData.value.qrType === 'SNS_LINK') && formData.value.targetContent && !isValidTargetContent.value) {
    return 'URL은 http:// 또는 https://로 시작해야 합니다.';
  }
  return '';
});

// 날짜 포맷 변환 (서버 -> 입력 필드)
const formatDateForInput = (dateString) => {
  if (!dateString) return '';

  // 서버에서 받은 'YYYY-MM-DD HH:MM:SS' 형식을 'YYYY-MM-DDTHH:MM' 형식으로 변환
  if (typeof dateString === 'string' && dateString.includes(' ')) {
    // 'YYYY-MM-DD HH:MM:SS' -> 'YYYY-MM-DDTHH:MM'
    const [datePart, timePart] = dateString.split(' ');
    const timeWithoutSeconds = timePart.split(':').slice(0, 2).join(':');
    return `${datePart}T${timeWithoutSeconds}`;
  }

  // 다른 형식의 날짜인 경우 기존 방식 사용
  try {
    const date = new Date(dateString);
    return date.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM 형식
  } catch (e) {
    console.error('날짜 변환 오류:', e);
    return '';
  }
};

// A4 캔버스 위치 제약 함수
const constrainA4CanvasPosition = (x, y, width, height) => {
  const parentContainer = document.querySelector('.a4-canvas-parent-container');
  if (!parentContainer) return { x, y };

  // 부모 컨테이너의 실제 내부 크기 계산 (패딩 제외)
  const parentStyle = window.getComputedStyle(parentContainer);
  const paddingLeft = parseInt(parentStyle.paddingLeft) || 20;
  const paddingTop = parseInt(parentStyle.paddingTop) || 20;
  const paddingRight = parseInt(parentStyle.paddingRight) || 20;
  const paddingBottom = parseInt(parentStyle.paddingBottom) || 20;

  const parentRect = parentContainer.getBoundingClientRect();
  const availableWidth = parentRect.width - paddingLeft - paddingRight;
  const availableHeight = parentRect.height - paddingTop - paddingBottom;

  // 위치 제약 (0 이상, 사용 가능한 영역 내)
  const maxX = Math.max(0, availableWidth - width);
  const maxY = Math.max(0, availableHeight - height);

  const constrainedX = Math.max(0, Math.min(x, maxX));
  const constrainedY = Math.max(0, Math.min(y, maxY));

  return { x: constrainedX, y: constrainedY };
};

// A4 캔버스 드래그 이벤트 핸들러
const onA4CanvasDrag = (e) => {
  const constrained = constrainA4CanvasPosition(e.x, e.y, a4CanvasWidth.value, a4CanvasHeight.value);
  a4CanvasX.value = constrained.x;
  a4CanvasY.value = constrained.y;
};

// A4 캔버스 크기 조절 이벤트 핸들러
const onA4CanvasResize = (e) => {
  // 부모 컨테이너 크기 확인
  const parentContainer = document.querySelector('.a4-canvas-parent-container');
  if (!parentContainer) return;

  const parentRect = parentContainer.getBoundingClientRect();
  const padding = 40; // 패딩 여유분
  const maxWidth = Math.max(200, parentRect.width - padding);
  const maxHeight = Math.max(200, parentRect.height - padding);

  // 현재 크기와 위치 (독립적으로 조절)
  let newWidth = Math.max(200, Math.min(maxWidth, e.width));
  let newHeight = Math.max(200, Math.min(maxHeight, e.height));
  let newX = e.x;
  let newY = e.y;

  // 최대 크기 제한
  if (newWidth > 1000) newWidth = 1000;
  if (newHeight > 1400) newHeight = 1400;

  // 위치 제약 적용 (부모 컨테이너 내부에 유지)
  const constrained = constrainA4CanvasPosition(newX, newY, newWidth, newHeight);

  // 값 업데이트
  a4CanvasWidth.value = newWidth;
  a4CanvasHeight.value = newHeight;
  a4CanvasX.value = constrained.x;
  a4CanvasY.value = constrained.y;

  // A4 캔버스 크기 정보를 FormData에 추가 (mm 단위로 변환하여 저장)
  if (formData.value) {
    formData.value.a4CanvasWidth = mmA4CanvasWidth.value;
    formData.value.a4CanvasHeight = mmA4CanvasHeight.value;
  }

  // 부모 컨테이너의 최소 높이를 A4 캔버스 크기에 맞게 조정
  nextTick(() => {
    const minHeight = Math.max(600, newHeight + 80); // 여백 80px 추가
    parentContainer.style.minHeight = `${minHeight}px`;
  });
};

// 날짜 포맷 변환 (입력 필드 -> 서버)
const formatDateForServer = (dateString) => {
  if (!dateString) return null;

  const date = new Date(dateString);

  // YYYY-MM-DD HH:MM:SS 형식으로 변환
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};


// 디자인 업데이트만 하는지 여부
const isDesignUpdateOnly = ref(false);
// 최초 타겟 콘텐츠 저장
const initialTargetContent = ref('');


// QR 코드 미리보기 생성 (qr-code-styling 사용)
const generateClientSideQrCode = async () => {
  if (!qrCodePreviewContainer.value) {
    return;
  }

  // 엑셀 샘플 데이터가 있는 경우 사용하거나, 이벤트 타입일 때 full URL 사용, 기타 타입은 targetContent 사용
  let contentToUse = '';
  let qrTypeToUse = formData.value.qrType;
  
  // 엑셀 샘플 데이터가 활성화된 경우 (엑셀 등록 후)
  if (excelSampleQrData.value.isActive && excelSampleQrData.value.targetContent) {
    contentToUse = excelSampleQrData.value.targetContent;
    qrTypeToUse = excelSampleQrData.value.qrType;
  }
  // 엑셀 샘플 데이터가 없는 일반 경우
  else if (formData.value.qrType === 'EVENT_ATTENDANCE') {
    if (!selectedEventId.value) {
      // 이벤트 미선택 시 미리보기 제거
      if (qrCodeInstance.value) {
        while (qrCodePreviewContainer.value.firstChild) {
          qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
        }
        qrCodeInstance.value = null;
      }
      return;
    }
    contentToUse = `${getFrontendDomain()}/event/${selectedEventId.value}`;
  } else {
    if (!formData.value.targetContent) {
      if (qrCodeInstance.value) {
        while (qrCodePreviewContainer.value.firstChild) {
          qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
        }
        qrCodeInstance.value = null;
      }
      return;
    }
    contentToUse = (isEditMode.value && isDesignUpdateOnly.value && initialTargetContent.value)
      ? initialTargetContent.value
      : formData.value.targetContent;
  }

  // 오류 복원 수준에 따라 QR 코드 버전 계산
  const calculatedVersion = calculateQrVersion(contentToUse, qrErrorCorrectionLevel.value);
  qrVersion.value = calculatedVersion; // 계산된 버전 적용
  
  // 엑셀 샘플 데이터가 사용되는 경우, 타입 정보 출력
  if (excelSampleQrData.value.isActive) {
  }

  // QR 코드 크기 고정 (250x250)
  const qrSize = 300;

  const logoSizeForLibrary = calculateLogoSize();

  // QR 코드 스타일링 라이브러리용 옵션 구성
  const baseDesignOptions = {
    width: qrSize,
    height: qrSize,
    data: contentToUse, // 엑셀 샘플 데이터 또는 일반 데이터
    margin: 0,
    dotsOptions: {
      color: qrColor.value,
      type: qrDotsStyle.value === 0 ? 'square' : 'dots'
    },
    backgroundOptions: {
      color: qrBgColor.value,
    },
    cornersSquareOptions: {
        color: qrEyeColor.value,
        type: qrEyeStyle.value === 0 ? 'square' : 'dot', // 'square' | 'dot' | 'extra-rounded'
    },
    cornersDotOptions: {
        color: qrEyeColor.value,
        type: qrEyeStyle.value === 0 ? undefined : 'dot' // Only apply type if dots selected for eye frame
    },
    image: logoPreview.value ? logoPreview.value : null,
    imageOptions: logoPreview.value ? {
      // 로고 크기를 원본 이미지 크기 대비 %로 계산
      // 오류 복원 수준에 관계없이 동일한 크기로 표시
      imageSize: logoSizeForLibrary,
      margin: 2, // 로고 주변 여백은 유지
      hideBackgroundDots: true
    } : {},
    // 오류 복원 수준 적용
    qrOptions: {
      errorCorrectionLevel: qrErrorCorrectionLevel.value, // L, M, Q, H
      typeNumber: calculatedVersion > 0 ? calculatedVersion : 0, // 0이면 자동 계산
      mode: 'Byte',
    },
    // [수정] removeQrCodeBlanks는 이 라이브러리에서 공식적으로 지원하는 옵션이 아닐 수 있습니다.
    // margin: 0 으로 충분할 것으로 예상됩니다. 만약 그래도 여백이 생긴다면 CSS로 처리합니다.
    // removeQrCodeBlanks: true // 이 옵션은 제거하거나 주석 처리
  };

  try {
    if (!qrCodeInstance.value) {
      // 컨테이너 비우기 (혹시 남아있을 수 있는 이전 요소 제거)
      while (qrCodePreviewContainer.value.firstChild) {
        qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
      }
      qrCodeInstance.value = new QRCodeStyling(baseDesignOptions);
      await qrCodeInstance.value.append(qrCodePreviewContainer.value);
    } else {
      await qrCodeInstance.value.update(baseDesignOptions);
    }
    
    // QR 코드 생성/업데이트 후 스캔율 계산
    scanReliability.value = calculateScanReliability();
    isQrCodeGenerated.value = true; // 생성/업데이트 성공 시 플래그 설정

    // QR 코드 이미지를 A4 캔버스에 표시하기 위한 로직
    // 현재 미리보기에 생성된 QR 코드 이미지 생성
    nextTick(() => {
      // A4 캔버스 사용 시 QR 코드 컨테이너 크기 강제 설정
      if (useA4Canvas.value) {
        forceQrContainerSize();
      }

      // generateClientSideQrCode에서 QR 코드가 생성되면, 5ms 뒤에 QR 코드 이미지를 가져옴
      setTimeout(() => {
        try {
          // QR 코드 이미지 엘리먼트 찾기
          const qrElement = qrCodePreviewContainer.value?.querySelector('canvas') || qrCodePreviewContainer.value?.querySelector('svg');

          if (qrElement) {
            // 캠버스가 있으면 그대로 dataURL로 변환
            if (qrElement.tagName === 'CANVAS') {
              formData.value.generatedQrDataUrl = qrElement.toDataURL('image/png');
            }
            // SVG의 경우
            else if (qrElement.tagName === 'SVG') {
              // SVG를 XML 문자열로 변환
              const serializer = new XMLSerializer();
              const svgStr = serializer.serializeToString(qrElement);
              // base64로 인코딩
              formData.value.generatedQrDataUrl = 'data:image/svg+xml;base64,' + btoa(svgStr);
            }

          } else {
            console.warn('QR 코드 이미지 엘리먼트를 찾을 수 없습니다.');
          }
        } catch (err) {
          console.error('QR 코드 이미지 처리 오류:', err);
        }
      }, 50); // 렌더링 시간을 위한 지연
    });

  } catch (error) {
     console.error("QR 코드 생성/업데이트 실패:", error);
     // 미리보기 영역에 오류 메시지 표시 등 처리 가능
     qrCodePreviewContainer.value.innerHTML = '<p style="color: red; text-align: center; padding: 10px;">QR 코드 미리보기 생성 중 오류가 발생했습니다.</p>';
     qrCodeInstance.value = null; // 오류 발생 시 인스턴스 초기화
  }
};


// 디바운스된 QR 코드 생성 함수 (디자인 변경 시 사용)
const generateQrCodeDebounced = (() => {
  let timeoutId;
  return () => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      // 디자인 업데이트 시에는 isDesignUpdateOnly 플래그 사용
      isDesignUpdateOnly.value = true;
      generateClientSideQrCode().finally(() => {
         isDesignUpdateOnly.value = false;
      });
    }, 300); // 300ms 디바운스
  };
})();

// 수정 모드일 경우 QR 코드 데이터 로드
const fetchQrCodeData = async () => {
  isLoading.value = true;
  error.value = '';
  try {
    const response = await getQrCodeById(route.params.qrCodeId);
    // 서버 응답 구조 확인 및 처리
    let qrData; // qrData 변수 선언 추가
    if (response && response.success === true && response.data) {
      // { success: true, data: {...} } 형태로 반환된 경우
      qrData = response.data;
    } else if (response && typeof response === 'object') {
      // 직접 데이터 객체가 반환된 경우
      qrData = response;
    } else {
      throw new Error('서버 응답 형식이 유효하지 않습니다.');
    }

    if (qrData && typeof qrData === 'object') {
      // 폼 데이터 설정 (날짜 변환 포함)
      formData.value = {
        ...(qrData || {}),
        validFromDate: qrData.validFromDate ? formatDateForInput(qrData.validFromDate) : '',
        validToDate: qrData.validToDate ? formatDateForInput(qrData.validToDate) : '',
      };

      // 디자인 옵션 파싱 및 적용
      if (qrData.designOptions) {
        try {
          const parsedOptions = JSON.parse(qrData.designOptions);
          qrColor.value = parsedOptions.dotsOptions?.color || '#000000';
          qrDotsStyle.value = parsedOptions.dotsOptions?.type === 'dots' ? 1 : 0;
          qrEyeColor.value = parsedOptions.dotsOptions?.eyeColor || qrColor.value; // 눈 색상 없으면 모듈 색상 따름
          qrEyeStyle.value = parsedOptions.dotsOptions?.eyeType === 'rounded' ? 1 : 0;
          qrBgColor.value = parsedOptions.backgroundOptions?.color || '#ffffff';
          qrErrorCorrectionLevel.value = parsedOptions.errorCorrectionLevel || 'M';

          // 로고 처리 (logoUrl 필드가 있는지 확인)
          if (qrData.logoUrl) {
              logoPreview.value = qrData.logoUrl; // 서버에서 제공된 로고 URL 사용
              // 로고 비율 적용 (옵션에 저장되어 있을 경우)
              if (parsedOptions.logoRatio) {
                 logoSize.value = parsedOptions.logoRatio * 100;
              }
          } else {
              logoPreview.value = ''; // 로고 없음
              logoSize.value = 10; // 기본 크기로 리셋
          }

        } catch (e) {
          console.error('기존 디자인 옵션 파싱 오류:', e);
          error.value = '기존 디자인 옵션을 불러오는 중 오류가 발생했습니다. 기본 디자인으로 표시됩니다.';
          // 파싱 실패 시 기본 디자인 값 유지
          resetDesignOptions(); // 오류 시 디자인 초기화
        }
      } else {
         // 디자인 옵션이 없는 경우 기본값 사용
         resetDesignOptions(); // 기본값으로 초기화
      }

      // 수정 모드에서는 초기 타겟 콘텐츠 저장
      initialTargetContent.value = formData.value.targetContent;

      // QR 코드 설치 위치 정보 파싱 (서버에서 위치 정보가 있는 경우)
      if (qrData.installationLocationLat && qrData.installationLocationLng) {
        locationData.value.latitude = qrData.installationLocationLat;
        locationData.value.longitude = qrData.installationLocationLng;
        locationData.value.address = qrData.installationLocation || '선택한 위치';
      }

      // QR 코드 설치 사진 경로 파싱 (서버에서 설치 사진 정보가 있는 경우)
      if (qrData.qrInstalledImagePath) {
        locationImagePreview.value = qrData.qrInstalledImagePath;
      }

      // QR 코드 타입이 '위치'일 때 타겟 위치 정보 파싱
      if (formData.value.qrType === 'LOCATION' && formData.value.targetContent) {
        // Google Maps URL 형식 파싱 시도 (https://maps.google.com/?q=위도,경도)
        const googleMapsMatch = formData.value.targetContent.match(/maps\.google\.com\/\?q=(-?\d+\.?\d*),(-?\d+\.?\d*)/);

        // geo: 형식 파싱 시도 (geo:위도,경도)
        const geoMatch = formData.value.targetContent.match(/geo:(-?\d+\.?\d*),(-?\d+\.?\d*)/);

        if (googleMapsMatch && googleMapsMatch.length >= 3) {
          // Google Maps URL 형식 파싱 성공
          targetLocationData.value.latitude = googleMapsMatch[1];
          targetLocationData.value.longitude = googleMapsMatch[2];
          targetLocationData.value.address = '선택한 위치';
        } else if (geoMatch && geoMatch.length >= 3) {
          // geo: 형식 파싱 성공 (이전 형식 지원)
          targetLocationData.value.latitude = geoMatch[1];
          targetLocationData.value.longitude = geoMatch[2];
          targetLocationData.value.address = '선택한 위치';

          // 이전 형식을 새 형식으로 변환
          formData.value.targetContent = `https://maps.google.com/?q=${geoMatch[1]},${geoMatch[2]}`;
        } else {
          console.warn('타겟 위치 정보 파싱 실패:', formData.value.targetContent);
          // 파싱 실패 시 기본값 설정 (수정 모드에서 오류 방지)
          if (isEditMode.value) {
            // 타겟 콘텐츠가 있지만 파싱 실패한 경우, 임시 위치 정보 설정
            const defaultLat = '37.5665';
            const defaultLng = '126.9780';
            targetLocationData.value.latitude = defaultLat;
            targetLocationData.value.longitude = defaultLng;
            targetLocationData.value.address = '서울시청 (기본값)';

            // 타겟 콘텐츠 재설정 (Google Maps URL 형식)
            formData.value.targetContent = `https://maps.google.com/?q=${defaultLat},${defaultLng}`;
          }
        }
      }
      // QR 코드 타입이 'WIFI'일 때 Wi-Fi 정보 파싱
      else if (formData.value.qrType === 'WIFI' && formData.value.targetContent) {
        // Wi-Fi QR 코드 형식 파싱 시도 (WIFI:S:<SSID>;T:<WPA|WEP|>;P:<PASSWORD>;H:<true|false>;;)
        try {
          const content = formData.value.targetContent;

          // SSID 추출
          const ssidMatch = content.match(/WIFI:S:([^;]*);/);
          if (ssidMatch && ssidMatch.length >= 2) {
            // 이스케이프된 문자 처리
            wifiData.value.ssid = ssidMatch[1].replace(/\\([;:,"\\])/g, '$1');
          }

          // 보안 유형 추출
          const typeMatch = content.match(/T:([^;]*);/);
          if (typeMatch && typeMatch.length >= 2) {
            wifiData.value.securityType = typeMatch[1];
          } else {
            wifiData.value.securityType = ''; // 보안 없음
          }

          // 비밀번호 추출
          const passwordMatch = content.match(/P:([^;]*);/);
          if (passwordMatch && passwordMatch.length >= 2) {
            // 이스케이프된 문자 처리
            wifiData.value.password = passwordMatch[1].replace(/\\([;:,"\\])/g, '$1');
          } else {
            wifiData.value.password = '';
          }

          // 숨겨진 네트워크 여부 추출
          const hiddenMatch = content.match(/H:([^;]*);/);
          if (hiddenMatch && hiddenMatch.length >= 2) {
            wifiData.value.hidden = hiddenMatch[1].toLowerCase() === 'true';
          } else {
            wifiData.value.hidden = false;
          }

        } catch (err) {
          console.warn('Wi-Fi 정보 파싱 실패:', err, formData.value.targetContent);
          // 파싱 실패 시 기본값 설정 (수정 모드에서 오류 방지)
          if (isEditMode.value) {
            wifiData.value = {
              ssid: 'Wi-Fi 네트워크',
              securityType: 'WPA',
              password: '',
              hidden: false
            };

            // 타겟 콘텐츠 재설정
            formData.value.targetContent = generateWifiQrContent();
          }
        }
      }
      // 랜딩 페이지 타입인 경우 처리
      else if (formData.value.qrType === 'LANDING_PAGE') {
        // 타겟 콘텐츠에서 랜딩 페이지 ID 추출
        // URL 형식이 서버주소/landing/ID 형태이뮼로 마지막 부분만 추출
        const match = formData.value.targetContent.match(/\/landing\/(\d+)/);
        if (match && match[1]) {
          selectedLandingPageId.value = match[1];
          // 랜딩 페이지 목록 가져오기
          await fetchLandingPages();

          // 수정 모드에서는 타겟 콘텐츠를 서버 주소가 포함된 형태로 유지
          // 이미 저장된 타겟 콘텐츠를 그대로 사용
          initialTargetContent.value = formData.value.targetContent;
        } else {
          // URL 형식이 아닌 경우 프론트엔드 서버 주소를 포함한 전체 URL 형식으로 설정 (예: http://localhost:9999/landing/{landingPageId})
          const frontendDomain = getFrontendDomain();
          formData.value.targetContent = `${frontendDomain}/landing/${formData.value.targetContent}`;
          initialTargetContent.value = formData.value.targetContent;

          // 타겟 콘텐츠에서 ID만 추출 시도
          const idMatch = formData.value.targetContent.match(/(\d+)$/);
          if (idMatch && idMatch[1]) {
            selectedLandingPageId.value = idMatch[1];
            await fetchLandingPages();
          }
        }
      }

      // 이벤트 타입일 경우 이벤트 선택 초기화 및 목록 로드
      if (formData.value.qrType === 'EVENT_ATTENDANCE') {
        // 서버 응답에서 연결된 이벤트 ID 추출 (다양한 필드명 처리)
        let eventId = '';

        // 가능한 모든 필드명 확인
        if (qrData.linkedEventId) {
          eventId = qrData.linkedEventId;
        } else if (qrData.linked_event_id) {
          eventId = qrData.linked_event_id;
        } 

        // 타겟 콘텐츠에서 이벤트 ID 추출 시도 (URL 형식인 경우)
        if ((!eventId || eventId === '') && qrData.targetContent) {
          const match = qrData.targetContent.match(/\/event\/(\d+)/);
          if (match && match[1]) {
            eventId = match[1];
          }
        }

        // 이벤트 목록 먼저 가져오기
        await fetchEvents();

        // 이벤트 목록을 가져온 후 이벤트 ID 설정
        if (eventId && eventId !== '') {
          // 문자열로 변환하여 비교 (숫자형과 문자열형 ID 모두 처리)
          const eventIdStr = String(eventId);

          // 이벤트 목록에 해당 ID가 있는지 확인
          const eventExists = events.value.some(event => {
            const eventIdFromList = String(event.eventId);
            const matches = eventIdFromList === eventIdStr;
            return matches;
          });

          if (eventExists) {
            selectedEventId.value = eventIdStr;

            // 선택된 이벤트 이름 로그
            const selectedEvent = events.value.find(event => String(event.eventId) === eventIdStr);
          } else {
            console.warn('이벤트 목록에 일치하는 ID가 없음:', eventIdStr);
            // 이벤트 목록에 없는 경우 빈 값으로 설정
            selectedEventId.value = '';
          }
        } else {
          console.warn('이벤트 ID가 없음');
          // 이벤트 ID가 없는 경우 빈 값으로 설정
          selectedEventId.value = '';
        }
      }

      // 데이터 로드 후 즉시 QR 코드 생성 시도
      await nextTick();
      await generateClientSideQrCode(); // await 추가하여 비동기 완료 기다림

      // isQrCodeGenerated는 generateClientSideQrCode 내부에서 설정됨

    } else {
      console.error('API 응답 데이터가 유효하지 않음:', response);
      error.value = '서버로부터 유효한 QR 코드 데이터를 받지 못했습니다.';
      isLoading.value = false; // 로딩 완료 처리
    }

  } catch (err) {
    console.error('QR 코드 데이터 로딩 또는 처리 중 오류:', err);
    error.value = `QR 코드 정보를 불러오는 데 실패했습니다: ${err.message}. 다시 시도해주세요.`;
    if (err.response?.status === 404) {
      error.value = '존재하지 않는 QR 코드입니다.';
    }
    isLoading.value = false; // 로딩 완료 처리
  } finally {
     // fetchQrCodeData 함수 내 로딩 상태 관리는 이 블록에서 하지 않음
     // generateClientSideQrCode 호출 후 로딩 상태 변경
      // 단, 에러 발생 시 isLoading을 false로 설정해야 함 (위 catch 블록에서 처리)
      // 성공적으로 generateClientSideQrCode가 끝나면 로딩 완료
      if (!error.value) {
          isLoading.value = false;
      }
  }
};


// 랜딩 페이지 목록 가져오기
const fetchLandingPages = async () => {
  if (!currentProject.value?.projectId) {
    landingPages.value = [];
    return;
  }

  isLoadingLandingPages.value = true;

  try {
    const result = await getLandingPages(currentProject.value.projectId);

    // 응답 구조 확인 및 처리
    if (result && Array.isArray(result)) {
      // 직접 배열이 반환된 경우
      landingPages.value = result;
    } else if (result && result.content && Array.isArray(result.content)) {
      // { content: [...] } 형태로 반환된 경우
      landingPages.value = result.content;
    } else if (result && result.data && Array.isArray(result.data)) {
      // { success: true, data: [...] } 형태로 반환된 경우
      landingPages.value = result.data;
    } else if (result && result.data && result.data.content && Array.isArray(result.data.content)) {
      // { success: true, data: { content: [...] } } 형태로 반환된 경우
      landingPages.value = result.data.content;
    } else {
      // 기타 예상치 못한 응답 구조
      console.warn('예상치 못한 랜딩 페이지 API 응답 구조:', result);
      landingPages.value = [];
    }

  } catch (err) {
    console.error('랜딩 페이지 목록 불러오기 실패:', err);
    landingPages.value = [];
    error.value = `랜딩 페이지 목록을 불러오는 중 오류가 발생했습니다: ${err.message}`;
  } finally {
    isLoadingLandingPages.value = false;
  }
};

// 이벤트 목록 가져오기
const fetchEvents = async () => {
  if (!currentProject.value?.projectId) {
    console.warn('프로젝트 ID가 없어 이벤트 목록을 가져올 수 없습니다.');
    events.value = [];
    return;
  }

  isLoadingEvents.value = true;
  try {
    // API 호출
    const response = await getEvents(currentProject.value.projectId);

    // 응답 구조 확인
    let eventItems = [];
    if (Array.isArray(response)) {
      // 직접 배열이 반환된 경우
      eventItems = response;
    } else {
      eventItems = [];
    }

    events.value = eventItems;

    // 이미 선택된 이벤트 ID가 있는 경우 유효성 검사
    if (selectedEventId.value) {
      const selectedIdStr = String(selectedEventId.value);

      const eventExists = events.value.some(event => String(event.eventId) === selectedIdStr);

      if (!eventExists) {
        console.warn(`선택된 이벤트 ID(${selectedIdStr})가 이벤트 목록에 없습니다.`);
        // 이벤트 목록에 없는 경우 빈 값으로 설정할 수도 있음
        // selectedEventId.value = '';
      }
    }
  } catch (err) {
    console.error('이벤트 목록 불러오기 실패:', err);
    events.value = [];
    error.value = `이벤트 목록을 불러오는 중 오류가 발생했습니다: ${err.message}`;
  } finally {
    isLoadingEvents.value = false;
  }
};

// 프론트엔드 서버 주소 가져오기
const getFrontendDomain = () => {
  // 현재 웹 페이지의 URL에서 프론트엔드 서버 주소를 가져오기
  const currentUrl = window.location.href;
  const url = new URL(currentUrl);
  // 프로토콜 + 도메인 + 포트 반환 (예: http://localhost:9999)
  return `${url.protocol}//${url.host}`;
};

// 선택한 랜딩 페이지가 변경되면 타겟 콘텐츠 업데이트
watch(selectedLandingPageId, (newValue) => {
  if (newValue && formData.value.qrType === 'LANDING_PAGE') {
    // 선택한 랜딩 페이지의 URL을 타겟 콘텐츠로 설정
    // 프론트엔드 서버 주소를 포함한 전체 URL 형식으로 설정 (예: http://localhost:9999/landing/{landingPageId})
    const frontendDomain = getFrontendDomain();
    formData.value.targetContent = `${frontendDomain}/landing/${newValue}`;
  }
});

// QR 코드 타입이 변경되면 관련 데이터 처리
watch(() => formData.value.qrType, (newType) => {
  // 수정 모드에서는 타입 변경 시 초기화하지 않음 (서버에서 받은 데이터 유지)
  if (isEditMode.value) {

    // 랜딩 페이지나 이벤트 타입으로 변경된 경우에만 관련 데이터 로드
    if (newType === 'LANDING_PAGE') {
      fetchLandingPages();
    } else if (newType === 'EVENT_ATTENDANCE') {
      fetchEvents();
    }
    return;
  }

  // 생성 모드에서의 처리
  if (newType === 'LANDING_PAGE') {
    fetchLandingPages();
    // 타겟 콘텐츠 초기화 (랜딩 페이지 선택 전)
    formData.value.targetContent = '';
    selectedLandingPageId.value = '';
  } else if (newType === 'EVENT_ATTENDANCE') {
    fetchEvents();
    // 타겟 콘텐츠 초기화 (이벤트 선택 전)
    formData.value.targetContent = '';
    selectedEventId.value = '';
  } else if (newType === 'LOCATION') {
    // 타겟 위치 정보 초기화
    targetLocationData.value = {
      latitude: '',
      longitude: '',
      address: ''
    };
    // 타겟 콘텐츠 초기화
    formData.value.targetContent = '';
  } else if (newType === 'WIFI') {
    // Wi-Fi 정보 초기화
    wifiData.value = {
      ssid: '',
      securityType: 'WPA',
      password: '',
      hidden: false
    };
    // 타겟 콘텐츠 초기화
    formData.value.targetContent = '';
  }
});

// 이벤트 선택 변경 시 QR 코드 미리보기 업데이트 (수정 모드에서는 업데이트하지 않음)
watch(selectedEventId, (newId) => {
  // 수정 모드에서는 이벤트 선택 변경에 반응하지 않음
  if (isEditMode.value) {
    return;
  }

  // 생성 모드에서 QR코드가 생성되었거나, 타겟 콘텐츠가 있을 때만 디자인 변경 시 업데이트
  if (!isEditMode.value && formData.value.qrType === 'EVENT_ATTENDANCE' && newId) {
    isQrCodeGenerated.value = false;
    generateClientSideQrCode();
  }
});

onMounted(() => {
  if (isEditMode.value) {
    fetchQrCodeData();
  } else {
    // 생성 모드
    isLoading.value = false;
    nextTick(() => {
      // 생성 모드에서는 처음에 빈 미리보기 또는 플레이스홀더 표시
      // 사용자가 타겟 콘텐츠를 입력하면 watch 콜백에서 생성됨
      // generateClientSideQrCode(); // 초기에는 호출 안 함
    });
  }
});

// 타겟 콘텐츠 최초 입력 시에만 QR 코드 생성
const isQrCodeGenerated = ref(false); // QR 코드가 성공적으로 생성되었는지 여부

watch(() => formData.value.targetContent, (newValue, oldValue) => {
  // 수정 모드에서는 타겟 콘텐츠가 변경되어도 QR 코드 미리보기 업데이트하지 않음
  if (isEditMode.value) {
      return; // 수정 모드에서는 타겟 콘텐츠 변경에 반응하지 않음
  }

  // 이벤트 선택으로 인한 타겟 콘텐츠 변경인지 확인
  const isEventUrlChange =
    newValue &&
    oldValue &&
    newValue.includes('/event/') &&
    oldValue.includes('/event/');

  // 이벤트 URL이 변경된 경우 QR 코드 미리보기 업데이트하지 않음
  if (isEventUrlChange) {
    return;
  }

  // 생성 모드에서 아직 QR코드가 생성되지 않았을 때
  if (newValue && newValue.trim() !== '') {
    // 내용이 있고, 이전 값이 비어있었거나 아직 생성 전일 때만 자동 생성
    if (!isQrCodeGenerated.value) {
      generateClientSideQrCode(); // 직접 호출 (디바운스 없이 즉시)
    }
  } else {
    // 내용이 비었을 때 미리보기 클리어
    if (qrCodeInstance.value && qrCodePreviewContainer.value) {
       while (qrCodePreviewContainer.value.firstChild) {
         qrCodePreviewContainer.value.removeChild(qrCodePreviewContainer.value.firstChild);
       }
       qrCodeInstance.value = null;
       isQrCodeGenerated.value = false; // 재생성 필요 상태
    }
  }
});

// QR 코드 스캔율 계산 함수
const calculateScanReliability = () => {
  // 기본 스캔율 (오류 복원 수준에 따라 다름)
  let reliability;

  // 오류 복원 수준에 따른 기본 스캔율 설정
  switch (qrErrorCorrectionLevel.value) {
    case 'L': // 낮음 (7% 복원)
      reliability = 80;
      break;
    case 'M': // 중간 (15% 복원)
      reliability = 90;
      break;
    case 'Q': // 높음 (25% 복원)
      reliability = 95;
      break;
    case 'H': // 매우 높음 (30% 복원)
      reliability = 98;
      break;
    default:
      reliability = 90; // 기본값
  }

  // 모듈과 배경색 간의 대비 계산 (가장 중요한 요소)
  const moduleContrastFactor = calculateColorContrast(qrColor.value, qrBgColor.value);

  // 눈(Eye)과 배경색 간의 대비 계산
  const eyeContrastFactor = calculateColorContrast(qrEyeColor.value, qrBgColor.value);

  // 모듈과 눈 색상이 동일한지 확인 (색상 유사도 계산)
  const moduleEyeSimilarity = calculateColorSimilarity(qrColor.value, qrEyeColor.value);

  // 모듈과 배경색이 매우 유사한 경우 (스캔 거의 불가능)
  if (moduleContrastFactor < 0.1) {
    // 모듈과 배경색이 거의 동일한 경우 스캔이 불가능하게 낮게 설정
    reliability = 10;
    return reliability; // 스캔이 불가능한 수준이므로 여기서 바로 반환
  }

  // 눈과 배경색이 매우 유사한 경우 (스캔 어려움)
  if (eyeContrastFactor < 0.1) {
    // 눈은 QR 코드의 중요한 부분이므로 스캔이 매우 어려움
    reliability = 30;
    return reliability;
  }

  // 색상 대비가 낮을수록 스캔율 감소 (최대 -40%)
  reliability -= (1 - moduleContrastFactor) * 40;

  // 눈 대비가 낮을수록 스캔율 추가 감소 (최대 -20%)
  reliability -= (1 - eyeContrastFactor) * 20;

  // 모듈과 눈의 색상이 다를 경우 스캔율 약간 감소 (최대 -10%)
  if (moduleEyeSimilarity < 0.9) { // 90% 이하로 유사하면
    reliability -= (1 - moduleEyeSimilarity) * 10;
  }

  // 눈(Eye) 모양이 동그라미일 경우 스캔율 약간 감소 (-5%)
  if (qrEyeStyle.value === 1) {
    reliability -= 5;
  }

  // 로고가 있을 경우 로고 크기에 비례하여 스캔율 감소 (최대 -25%)
  if (logoPreview.value) {
    reliability -= (logoSize.value / 100) * 25; // 로고 크기가 클수록 스캔율 감소
  }

  // 데이터 길이에 따른 스캔율 감소 (데이터가 길수록 스캔이 어려움)
  const contentLength = formData.value.targetContent ? formData.value.targetContent.length : 0;
  if (contentLength > 0) {
    // 오류 복원 수준에 따라 다른 감소율 적용
    let lengthPenalty = 0;

    // 오류 복원 수준이 낮을수록 데이터 길이에 더 민감함
    switch (qrErrorCorrectionLevel.value) {
      case 'L': // 낮음 (7% 복원)
        lengthPenalty = Math.min(20, contentLength / 10); // 최대 20% 감소
        break;
      case 'M': // 중간 (15% 복원)
        lengthPenalty = Math.min(15, contentLength / 15); // 최대 15% 감소
        break;
      case 'Q': // 높음 (25% 복원)
        lengthPenalty = Math.min(10, contentLength / 20); // 최대 10% 감소
        break;
      case 'H': // 매우 높음 (30% 복원)
        lengthPenalty = Math.min(5, contentLength / 30); // 최대 5% 감소
        break;
    }

    reliability -= lengthPenalty;
  }

  // 스캔율은 최소 5%, 최대 99%로 제한
  reliability = Math.max(5, Math.min(99, Math.round(reliability)));

  return reliability;
};

// 색상 대비 계산 함수 (0~1 사이 값 반환, 1이 최대 대비)
const calculateColorContrast = (color1, color2) => {
  // 색상 코드를 RGB 값으로 변환
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);

  if (!rgb1 || !rgb2) return 0; // 유효하지 않은 색상 입력 시

  // 각 색상의 밝기 계산 (0~255)
  const brightness1 = (rgb1.r * 299 + rgb1.g * 587 + rgb1.b * 114) / 1000;
  const brightness2 = (rgb2.r * 299 + rgb2.g * 587 + rgb2.b * 114) / 1000;

  // 밝기 차이의 절대값 (0~255)
  const brightnessDiff = Math.abs(brightness1 - brightness2);

  // 밝기 차이를 0~1 사이 값으로 정규화
  return brightnessDiff / 255;
};

// 색상 유사도 계산 함수 (0~1 사이 값 반환, 1이 같은 색상)
const calculateColorSimilarity = (color1, color2) => {
  // 두 색상이 정확히 같은 경우
  if (color1 === color2) return 1.0;

  // 색상 코드를 RGB 값으로 변환
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);

   if (!rgb1 || !rgb2) return 0; // 유효하지 않은 색상 입력 시

  // 각 RGB 채널의 차이 계산 (0~255)
  const rDiff = Math.abs(rgb1.r - rgb2.r);
  const gDiff = Math.abs(rgb1.g - rgb2.g);
  const bDiff = Math.abs(rgb1.b - rgb2.b);

  // 총 차이 (0~765)
  const totalDiff = rDiff + gDiff + bDiff;

  // 차이를 0~1 사이 값으로 정규화 (1에 가까울수록 유사함)
  return 1 - (totalDiff / 765);
};

// HEX 색상 코드를 RGB 객체로 변환하는 함수
const hexToRgb = (hex) => {
  if (!hex || typeof hex !== 'string') return null; // 입력값 검증

  // # 제거
  hex = hex.replace(/^#/, '');

  // 3자리 HEX -> 6자리 변환
  if (hex.length === 3) {
    hex = hex.split('').map(char => char + char).join('');
  }

  if (hex.length !== 6) return null; // 유효하지 않은 길이

  // RGB 값 추출
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // 유효한 숫자인지 확인
  if (isNaN(r) || isNaN(g) || isNaN(b)) return null;

  return { r, g, b };
};


// 스캔율에 따른 CSS 클래스 계산
const getScanReliabilityClass = computed(() => {
  const reliability = scanReliability.value;
  if (reliability >= 90) return 'reliability-high';
  if (reliability >= 75) return 'reliability-medium';
  if (reliability >= 40) return 'reliability-low';
  return 'reliability-critical';
});

// 스캔율에 따른 색상 계산
const getScanReliabilityColor = computed(() => {
   const reliability = scanReliability.value;
  if (reliability >= 90) return '#4CAF50'; // 녹색
  if (reliability >= 75) return '#FFC107'; // 노란색
  if (reliability >= 40) return '#F44336'; // 빨간색
  return '#9C27B0'; // 보라색 (스캔 불가능 수준)
});

// 스캔율에 따른 메시지 계산
const getScanReliabilityMessage = computed(() => {
   const reliability = scanReliability.value;
  if (reliability >= 90) return '스캔 신뢰도가 높습니다. 대부분의 기기에서 잘 인식됩니다.';
  if (reliability >= 75) return '스캔 신뢰도가 보통입니다. 일부 기기에서 인식이 어려울 수 있습니다.';
  if (reliability >= 40) return '스캔 신뢰도가 낮습니다. 많은 기기에서 인식이 어려울 수 있습니다.';
  return '스캔이 거의 불가능합니다. 모듈과 배경색의 대비를 높여주세요.';
});

// 디자인 옵션 변경 시 QR 코드 재생성 및 스캔율 업데이트 (디바운스 적용)
watch([qrColor, qrBgColor, qrEyeColor, qrDotsStyle, qrEyeStyle, logoPreview, logoSize, qrErrorCorrectionLevel], () => {
  // 수정 모드에서는 디자인 옵션 변경에 반응하지 않음
  if (isEditMode.value) {
    return;
  }

  // 생성 모드에서 QR코드가 생성되었거나, 타겟 콘텐츠가 있을 때만 디자인 변경 시 업데이트
  if (isQrCodeGenerated.value || (formData.value.targetContent && formData.value.targetContent.trim() !== '')) {
    // 디자인 업데이트 시에는 isDesignUpdateOnly 플래그 사용
    isDesignUpdateOnly.value = true;
    generateClientSideQrCode().finally(() => {
      isDesignUpdateOnly.value = false;
    });
  }
}, { deep: true });

// QR 코드 컨테이너 크기 강제 설정 함수
const forceQrContainerSize = () => {
  nextTick(() => {
    const qrContainer = document.querySelector('.qr-draggable-container');
    if (qrContainer) {
      // 강제로 크기 설정
      qrContainer.style.width = `${qrWidth.value}px`;
      qrContainer.style.height = `${qrHeight.value}px`;
      qrContainer.style.left = `${qrPositionX.value}px`;
      qrContainer.style.top = `${qrPositionY.value}px`;
    }
  });
};

// A4 캔버스 사용 여부 변경 시 QR 코드 미리보기 업데이트
watch(useA4Canvas, (newValue) => {
  if (newValue) {
    // A4 캔버스 활성화 시 QR 코드 위치 및 크기 강제 초기화
    nextTick(() => {
      // QR 코드 크기와 위치 강제 설정
      qrPositionX.value = 50;
      qrPositionY.value = 50;
      qrWidth.value = 100;
      qrHeight.value = 100;

      // DOM 크기 강제 설정
      forceQrContainerSize();

      // DOM 업데이트 후 QR 코드 생성
      if (formData.value.targetContent || excelSampleQrData.value.isActive || (formData.value.qrType === 'EVENT_ATTENDANCE' && selectedEventId.value)) {
        generateQrCodeDebounced();
      }
    });
  }
});

// 배경 이미지 사용 여부 변경 시 처리
watch(useBackgroundImage, (newValue) => {
  if (!newValue) {
    // 배경 이미지 비활성화 시 관련 데이터 초기화
    backgroundImagePreview.value = null;
    backgroundImageFile.value = null;
    backgroundFitMode.value = 'fit';
    if (backgroundInput.value) {
      backgroundInput.value.value = '';
    }
  }
});

// 로고 파일 처리
const handleLogoUpload = (event) => {
  const file = event.target.files[0];
  if (file) {
    if (file.size > 1024 * 1024) { // 1MB 제한
      error.value = '로고 파일 크기는 1MB를 초과할 수 없습니다.';
      // 파일 입력 초기화
      if (logoInput.value) logoInput.value.value = '';
      return;
    }
    logoFile.value = file; // 실제 파일 저장 (업로드용)
    const reader = new FileReader();
    reader.onload = (e) => {
      // 원본 이미지 크기 가져오기
      const img = new Image();
      img.onload = () => {
        // 원본 이미지 크기 저장
        originalLogoSize.value = {
          width: img.width,
          height: img.height
        };
      };
      img.src = e.target.result;
      logoPreview.value = e.target.result; // Base64 미리보기 URL 설정
      // logoPreview가 변경되면 watch 콜백이 트리거되어 QR 업데이트
    };
    reader.readAsDataURL(file);
    error.value = ''; // 에러 메시지 초기화
  } else {
    // 파일 선택 취소 시 로고 제거 로직 수행 안함 (removeLogo 버튼으로만 제거)
    // removeLogo(); // 여기서 호출하지 않음
  }
};

// 배경 이미지 관련 변수
// QR 코드 디자인 옵션
// 배경 이미지 관련 변수
const backgroundInput = ref(null); // 배경 이미지 입력 필드 ref
const backgroundImageFile = ref(null);
const backgroundImagePreview = ref(null);
const backgroundFitMode = ref('fit'); // 기본 맞춤 모드: 'fit' (다른 옵션: 'fill', 'original')

// QR 코드 드래그 및 리사이즈 관련 변수
const qrPositionX = ref(50); // A4 캔버스 내에서 QR 코드의 초기 X 위치 (픽셀)
const qrPositionY = ref(50); // A4 캔버스 내에서 QR 코드의 초기 Y 위치 (픽셀)
const qrWidth = ref(100); // QR 코드 이미지의 초기 너비 (픽셀)
const qrHeight = ref(100); // QR 코드 이미지의 초기 높이 (픽셀)

// 배경 이미지 드래그 및 리사이즈 관련 변수
const backgroundPositionX = ref(0); // A4 캔버스 내에서 배경 이미지의 초기 X 위치 (픽셀)
const backgroundPositionY = ref(0); // A4 캔버스 내에서 배경 이미지의 초기 Y 위치 (픽셀)
const backgroundWidth = ref(300); // 배경 이미지의 초기 너비 (픽셀)
const backgroundHeight = ref(200); // 배경 이미지의 초기 높이 (픽셀)

// A4 캔버스 크기 조절 관련 변수
const a4CanvasWidth = ref(400); // A4 캔버스의 초기 너비 (픽셀)
const a4CanvasHeight = ref(500); // A4 캔버스의 초기 높이 (픽셀, 자유 조절 가능)
const a4CanvasX = ref(0); // A4 캔버스의 X 위치 (부모 컨테이너 내에서)
const a4CanvasY = ref(20); // A4 캔버스의 Y 위치 (부모 컨테이너 내에서)

// A4 캔버스 초기 위치 설정 함수
const initializeA4CanvasPosition = () => {
  nextTick(() => {
    const parentContainer = document.querySelector('.a4-canvas-parent-container');
    if (parentContainer) {
      const parentRect = parentContainer.getBoundingClientRect();
      const padding = 20; // 부모 컨테이너 패딩

      // 부모 컨테이너 내부에서 중앙 정렬
      const availableWidth = parentRect.width - (padding * 2);
      const availableHeight = parentRect.height - (padding * 2);

      a4CanvasX.value = Math.max(0, (availableWidth - a4CanvasWidth.value) / 2);
      a4CanvasY.value = Math.max(0, (availableHeight - a4CanvasHeight.value) / 2);
    }
  });
};

// A4 캔버스 사용 여부 변경 감지
watch(useA4Canvas, (newValue) => {
  if (newValue) {
    // A4 캔버스가 활성화되면 초기 위치 설정
    setTimeout(() => {
      initializeA4CanvasPosition();
    }, 100); // DOM 렌더링 대기
  }
});

// A4 캔버스 관련 변수
const a4CanvasContainer = ref(null); // A4 캔버스 컨테이너 DOM 참조

// A4 캔버스를 고화질 이미지로 생성하는 함수
const generateA4CanvasImage = async () => {
  if (!a4CanvasContainer.value) {
    console.warn('A4 캔버스 컨테이너를 찾을 수 없습니다.');
    return null;
  }

  try {
    // html2canvas 동적 import
    const html2canvas = (await import('html2canvas')).default;
    
    // A4 캔버스 영역을 고화질로 캡처
    const canvas = await html2canvas(a4CanvasContainer.value, {
      scale: 3, // 고화질을 위한 스케일 증가 (3배)
      useCORS: true, // CORS 이슈 해결
      allowTaint: true,
      backgroundColor: '#ffffff', // 배경색 설정
      width: a4CanvasContainer.value.offsetWidth,
      height: a4CanvasContainer.value.offsetHeight,
      logging: false // 로깅 비활성화
    });

    // Canvas를 Blob으로 변환
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (blob) {
          // Blob을 File 객체로 변환
          const file = new File([blob], 'a4-canvas-image.png', {
            type: 'image/png',
            lastModified: Date.now()
          });
          resolve(file);
        } else {
          console.warn('Canvas를 Blob으로 변환하는데 실패했습니다.');
          resolve(null);
        }
      }, 'image/png', 0.95); // PNG 형식, 95% 품질
    });
  } catch (error) {
    console.error('A4 캔버스 이미지 생성 중 오류 발생:', error);
    return null;
  }
};

// A4 용지 실제 크기 (mm 단위)
const A4_WIDTH_MM = 210;
const A4_HEIGHT_MM = 297;

// 픽셀을 밀리미터로 변환하는 함수
const pixelToMm = (pixel, isWidth = true) => {
  if (!a4CanvasContainer.value) return 0;

  const containerRect = a4CanvasContainer.value.getBoundingClientRect();
  const containerSize = isWidth ? containerRect.width : containerRect.height;
  const a4Size = isWidth ? A4_WIDTH_MM : A4_HEIGHT_MM;

  return (pixel / containerSize) * a4Size;
};

// 밀리미터를 픽셀로 변환하는 함수
const mmToPixel = (mm, isWidth = true) => {
  if (!a4CanvasContainer.value) return 0;

  const containerRect = a4CanvasContainer.value.getBoundingClientRect();
  const containerSize = isWidth ? containerRect.width : containerRect.height;
  const a4Size = isWidth ? A4_WIDTH_MM : A4_HEIGHT_MM;

  return (mm / a4Size) * containerSize;
};

// 현재 QR코드 위치를 mm 단위로 계산
const mmPositionX = computed(() => {
  return Math.round(pixelToMm(qrPositionX.value, true) * 10) / 10;
});

const mmPositionY = computed(() => {
  return Math.round(pixelToMm(qrPositionY.value, false) * 10) / 10;
});

const mmWidth = computed(() => {
  return Math.round(pixelToMm(qrWidth.value, true) * 10) / 10;
});

const mmHeight = computed(() => {
  return Math.round(pixelToMm(qrHeight.value, false) * 10) / 10;
});

// 배경 이미지 mm 단위 위치 및 크기
const mmBackgroundPositionX = computed(() => {
  return Math.round(pixelToMm(backgroundPositionX.value, true) * 10) / 10;
});

const mmBackgroundPositionY = computed(() => {
  return Math.round(pixelToMm(backgroundPositionY.value, false) * 10) / 10;
});

const mmBackgroundWidth = computed(() => {
  return Math.round(pixelToMm(backgroundWidth.value, true) * 10) / 10;
});

const mmBackgroundHeight = computed(() => {
  return Math.round(pixelToMm(backgroundHeight.value, false) * 10) / 10;
});

// A4 캔버스 mm 단위 크기
const mmA4CanvasWidth = computed(() => {
  return Math.round(pixelToMm(a4CanvasWidth.value, true) * 10) / 10;
});

const mmA4CanvasHeight = computed(() => {
  return Math.round(pixelToMm(a4CanvasHeight.value, false) * 10) / 10;
});

// QR 코드 드래그 이벤트 핸들러 (경계 체크 포함)
const onDragWithBounds = (e) => {
  if (!a4CanvasContainer.value) return;

  const containerRect = a4CanvasContainer.value.getBoundingClientRect();
  const maxX = containerRect.width - qrWidth.value;
  const maxY = containerRect.height - qrHeight.value;

  // 경계 내에서만 이동 가능하도록 제한
  const boundedX = Math.max(0, Math.min(e.x, maxX));
  const boundedY = Math.max(0, Math.min(e.y, maxY));

  qrPositionX.value = boundedX;
  qrPositionY.value = boundedY;

  // QR 코드 위치 정보를 FormData에 추가 (mm 단위로 변환하여 저장)
  if (formData.value) {
    formData.value.qrPositionX = mmPositionX.value;
    formData.value.qrPositionY = mmPositionY.value;
    formData.value.qrWidth = mmWidth.value;
    formData.value.qrHeight = mmHeight.value;
  }
};

// QR 코드 드래그 이벤트 핸들러 (기본)
const onDrag = (e) => {
  // e는 이벤트 객체로, x와 y를 포함함
  qrPositionX.value = e.x;
  qrPositionY.value = e.y;

  // QR 코드 위치 정보를 FormData에 추가 (서버에 전송을 위해)
  if (formData.value) {
    formData.value.qrPositionX = e.x;
    formData.value.qrPositionY = e.y;
  }
};

// QR 코드 크기 조절 이벤트 핸들러 (일반 사용)
const onResize = (e) => {
  // e는 이벤트 객체로, x, y, width, height를 포함함
  qrPositionX.value = e.x;
  qrPositionY.value = e.y;
  qrWidth.value = e.width;
  qrHeight.value = e.height;
  
  // QR 코드 위치와 크기 정보를 FormData에 추가 (서버에 전송을 위해)
  if (formData.value) {
    formData.value.qrPositionX = e.x;
    formData.value.qrPositionY = e.y;
    formData.value.qrWidth = e.width;
    formData.value.qrHeight = e.height;
  }
};

// QR 코드 크기 조절 이벤트 핸들러 (정사각형 비율 유지)
const onResizeWithAspectRatio = (e) => {
  if (!a4CanvasContainer.value) return;

  // QR코드는 정사각형이어야 하므로 가로와 세로를 동일하게 처리
  // 더 큰 값을 기준으로 하여 사용자가 늘리려는 의도를 반영
  const size = Math.max(e.width, e.height);
  
  // 최소/최대 크기 제한
  const constrainedSize = Math.max(50, Math.min(200, size));

  // 경계 체크
  const containerRect = a4CanvasContainer.value.getBoundingClientRect();
  const maxX = containerRect.width - constrainedSize;
  const maxY = containerRect.height - constrainedSize;

  const boundedX = Math.max(0, Math.min(e.x, maxX));
  const boundedY = Math.max(0, Math.min(e.y, maxY));

  // 강제로 정사각형 크기 적용
  qrPositionX.value = boundedX;
  qrPositionY.value = boundedY;
  qrWidth.value = constrainedSize;
  qrHeight.value = constrainedSize;

  // QR 코드 위치와 크기 정보를 FormData에 추가 (mm 단위로 변환하여 저장)
  if (formData.value && useA4Canvas.value) {
    formData.value.qrPositionX = mmPositionX.value;
    formData.value.qrPositionY = mmPositionY.value;
    formData.value.qrWidth = mmWidth.value;
    formData.value.qrHeight = mmHeight.value;
  } else if (formData.value) {
    // A4 캔버스를 사용하지 않는 경우 픽셀 단위로 저장
    formData.value.qrPositionX = boundedX;
    formData.value.qrPositionY = boundedY;
    formData.value.qrWidth = constrainedSize;
    formData.value.qrHeight = constrainedSize;
  }
};

// 배경 이미지 맞춤 모드 설정
const setBackgroundFitMode = (mode) => {
  backgroundFitMode.value = mode;
};

// 배경 이미지 업로드 처리
const handleBackgroundUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 미리보기 URL 생성
  const reader = new FileReader();
  
  reader.onload = (e) => {
    backgroundImagePreview.value = e.target.result;
    backgroundImageFile.value = file;
  };
  
  reader.readAsDataURL(file);
};

// 배경 이미지 제거
const removeBackgroundImage = () => {
  backgroundImagePreview.value = null;
  backgroundImageFile.value = null;
  // 배경 이미지 위치 및 크기 초기화
  backgroundPositionX.value = 0;
  backgroundPositionY.value = 0;
  backgroundWidth.value = 300;
  backgroundHeight.value = 200;
};

// 배경 이미지 드래그 이벤트 핸들러
const onBackgroundDrag = (e) => {
  if (!a4CanvasContainer.value) return;

  const containerRect = a4CanvasContainer.value.getBoundingClientRect();
  const maxX = containerRect.width - backgroundWidth.value;
  const maxY = containerRect.height - backgroundHeight.value;

  // 경계 내에서만 이동 가능하도록 제한 (배경 이미지는 일부가 캔버스 밖으로 나갈 수 있도록 허용)
  const boundedX = Math.max(-backgroundWidth.value * 0.5, Math.min(e.x, maxX + backgroundWidth.value * 0.5));
  const boundedY = Math.max(-backgroundHeight.value * 0.5, Math.min(e.y, maxY + backgroundHeight.value * 0.5));

  backgroundPositionX.value = boundedX;
  backgroundPositionY.value = boundedY;

  // 배경 이미지 위치 정보를 FormData에 추가 (mm 단위로 변환하여 저장)
  if (formData.value) {
    formData.value.backgroundPositionX = mmBackgroundPositionX.value;
    formData.value.backgroundPositionY = mmBackgroundPositionY.value;
    formData.value.backgroundWidth = mmBackgroundWidth.value;
    formData.value.backgroundHeight = mmBackgroundHeight.value;
  }
};

// 배경 이미지 크기 조절 이벤트 핸들러
const onBackgroundResize = (e) => {
  if (!a4CanvasContainer.value) return;

  const containerRect = a4CanvasContainer.value.getBoundingClientRect();
  
  // 최소 크기 제한
  const constrainedWidth = Math.max(100, e.width);
  const constrainedHeight = Math.max(100, e.height);
  
  // 경계 체크 (배경 이미지는 캔버스 밖으로 나갈 수 있도록 허용)
  const maxX = containerRect.width - constrainedWidth;
  const maxY = containerRect.height - constrainedHeight;
  
  const boundedX = Math.max(-constrainedWidth * 0.5, Math.min(e.x, maxX + constrainedWidth * 0.5));
  const boundedY = Math.max(-constrainedHeight * 0.5, Math.min(e.y, maxY + constrainedHeight * 0.5));

  backgroundPositionX.value = boundedX;
  backgroundPositionY.value = boundedY;
  backgroundWidth.value = constrainedWidth;
  backgroundHeight.value = constrainedHeight;

  // 배경 이미지 위치와 크기 정보를 FormData에 추가 (mm 단위로 변환하여 저장)
  if (formData.value && useA4Canvas.value) {
    formData.value.backgroundPositionX = mmBackgroundPositionX.value;
    formData.value.backgroundPositionY = mmBackgroundPositionY.value;
    formData.value.backgroundWidth = mmBackgroundWidth.value;
    formData.value.backgroundHeight = mmBackgroundHeight.value;
  } else if (formData.value) {
    // A4 캔버스를 사용하지 않는 경우 픽셀 단위로 저장
    formData.value.backgroundPositionX = boundedX;
    formData.value.backgroundPositionY = boundedY;
    formData.value.backgroundWidth = constrainedWidth;
    formData.value.backgroundHeight = constrainedHeight;
  }
};

// 로고 제거
const removeLogo = () => {
  logoPreview.value = null;
  logoFile.value = null;
  
  // 로고 제거 후 QR 코드 미리보기 업데이트
  generateQrCodeDebounced();
  
  // 스캔율 재계산
  calculateScanReliability();
};

// 로고 크기를 계산하는 함수 (원본 이미지 크기 대비 %)
const calculateLogoSize = () => {
  // 라이브러리는 imageSize를 0~1 사이 값으로 기대함 (QR 코드 크기 대비 비율)

  // 로고가 없으면 0 반환 (라이브러리는 image가 null이면 imageSize 무시)
  if (!logoPreview.value) {
    return 0;
  }

  // 사용자 설정값 (logoSize: 10~100)을 라이브러리 비율 (0.1 ~ 1.0)로 변환
  const userRatio = logoSize.value / 100;

  // 로고가 너무 커지는 것을 방지하기 위한 최대 비율 설정 (예: 0.4 = 40%)
  // 로고가 너무 크면 스캔율이 급격히 떨어지므로 적절한 상한선 설정이 중요합니다.
  // 필요에 따라 이 값을 조절하세요.
  const maxSizeRatio = 0.4;
  const finalRatio = Math.min(userRatio, maxSizeRatio);

  // 최종 계산된 비율 반환
  return finalRatio;
};


// 선택된 이벤트 이름 가져오기 (필요한 경우 사용)
// const getSelectedEventName = () => {
//   if (!selectedEventId.value || !events.value || events.value.length === 0) {
//     return '';
//   }
//
//   const selectedEvent = events.value.find(event => event.eventId == selectedEventId.value);
//   return selectedEvent ? selectedEvent.eventName : '알 수 없는 이벤트';
// };

// QR 코드 설치 위치 선택용 카카오맵 모달 열기
const openKakaoMap = () => {
  showKakaoMapModal.value = true;
};

// QR 코드 설치 위치 선택 처리
const handleLocationSelect = (location) => {
  locationData.value = {
    latitude: location.lat,
    longitude: location.lng,
    address: location.address
  };

};

// 위치 이미지 업로드 처리
const handleLocationImageUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 파일 크기 확인 (5MB 제한)
  if (file.size > 5 * 1024 * 1024) {
    alert('파일 크기가 너무 큽니다. 5MB 이하의 이미지를 업로드해주세요.');
    if (locationImageInput.value) {
      locationImageInput.value.value = '';
    }
    return;
  }

  // 파일 형식 확인
  if (!file.type.match('image.*')) {
    alert('이미지 파일만 업로드 가능합니다.');
    if (locationImageInput.value) {
      locationImageInput.value.value = '';
    }
    return;
  }

  // 파일 저장 및 미리보기 URL 생성
  locationImageFile.value = file;
  const reader = new FileReader();
  reader.onload = (e) => {
    locationImagePreview.value = e.target.result;
  };
  reader.readAsDataURL(file);

};

// 파일 입력 필드 준비 (설치 위치 사진 선택 버튼 클릭 시 호출)
const prepareImageUpload = () => {
  // 파일 입력 필드를 새로 렌더링하기 위해 잠시 숨겼다가 다시 보여줌
  showFileInput.value = false;
  // 키 값을 변경하여 Vue가 엘리먼트를 새로 생성하도록 함
  fileInputKey.value++;
  
  // 다음 렌더링 사이클에서 파일 입력 필드를 다시 표시
  setTimeout(() => {
    showFileInput.value = true;
    
    // 파일 입력 필드가 마운트된 후 클릭 이벤트 발생
    nextTick(() => {
      if (locationImageInput.value) {
        locationImageInput.value.click();
      }
    });
  }, 50);
};

// 위치 이미지 제거
const removeLocationImage = () => {
  locationImageFile.value = null;
  locationImagePreview.value = '';
  
  // 파일 입력 필드를 새로 렌더링하기 위해 키 값 변경
  fileInputKey.value++;
  
};

// QR 코드 타겟 위치 선택용 카카오맵 모달 열기
const openTargetLocationMap = () => {
  showTargetLocationMapModal.value = true;
};

// QR 코드 타겟 위치 선택 처리
const handleTargetLocationSelect = (location) => {
  targetLocationData.value = {
    latitude: location.lat,
    longitude: location.lng,
    address: location.address
  };

  // 위치 타입일 때 타겟 콘텐츠 설정 (Google Maps URL 형식)
  if (formData.value.qrType === 'LOCATION') {
    // Google Maps URL 형식 사용 (더 넓은 호환성)
    formData.value.targetContent = `https://maps.google.com/?q=${location.lat},${location.lng}`;

    // QR 코드 미리보기 업데이트
    generateClientSideQrCode();
  }
};

// QR 코드 설치 위치 정보 변경 감지 (디버깅용)
watch([() => locationData.value.latitude, () => locationData.value.longitude], ([newLat, newLng]) => {
  if (newLat && newLng) {
  }
});

// QR 코드 타겟 위치 정보 변경 감지
watch([() => targetLocationData.value.latitude, () => targetLocationData.value.longitude], ([newLat, newLng]) => {
  if (newLat && newLng && formData.value.qrType === 'LOCATION') {
    // 위치 타입일 때 타겟 콘텐츠 설정 (Google Maps URL 형식)
    formData.value.targetContent = `https://maps.google.com/?q=${newLat},${newLng}`;

    // QR 코드 미리보기 업데이트
    if (isQrCodeGenerated.value) {
      generateClientSideQrCode();
    }
  }
});

// 비밀번호 표시/숨기기 토글
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// Wi-Fi 정보를 QR 코드 형식으로 변환
const generateWifiQrContent = () => {
  const { ssid, securityType, password, hidden } = wifiData.value;

  // SSID와 비밀번호에 특수 문자가 있을 경우 처리
  const escapedSsid = ssid.replace(/[;:,"\\]/g, '\\$&');
  const escapedPassword = password.replace(/[;:,"\\]/g, '\\$&');

  // Wi-Fi QR 코드 형식: WIFI:S:<SSID>;T:<WPA|WEP|>;P:<PASSWORD>;H:<true|false>;;
  let wifiString = `WIFI:S:${escapedSsid};`;

  // 보안 유형 추가 (있는 경우)
  if (securityType) {
    wifiString += `T:${securityType};`;
  }

  // 비밀번호 추가 (있는 경우)
  if (password && securityType) {
    wifiString += `P:${escapedPassword};`;
  }

  // 숨겨진 네트워크 여부 추가
  wifiString += `H:${hidden ? 'true' : 'false'};;`;

  return wifiString;
};

// Wi-Fi 정보 변경 감지
watch([() => wifiData.value.ssid, () => wifiData.value.securityType, () => wifiData.value.password, () => wifiData.value.hidden],
  () => {
    if (formData.value.qrType === 'WIFI' && wifiData.value.ssid) {
      // Wi-Fi 타입일 때 타겟 콘텐츠 설정
      formData.value.targetContent = generateWifiQrContent();

      // QR 코드 미리보기 업데이트
      if (isQrCodeGenerated.value) {
        generateClientSideQrCode();
      }
    }
  }
);

// 취소 버튼 클릭 시 목록 페이지로 돌아가기
const goBack = () => {
  router.push({ name: 'qr-list' }); // QR 코드 목록으로 이동
};

// 엑셀 템플릿 다운로드 함수
const downloadExcelTemplate = () => {
  // 엑셀 템플릿 생성
  const worksheet = XLSX.utils.aoa_to_sheet([
    ['QR코드 타입(URL, TEXT, SNS_LINL 만 입력)', '타겟 콘텐츠'], // 컬럼 헤더
    ['URL', 'https://www.example.com'], // 예시 데이터 1
    ['TEXT', '내용을 텍스트로 입력해주세요.'], // 예시 데이터 2
    ['SNS_LINK', 'https://www.instagram.com/example'] // 예시 데이터 3
  ]);
  
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, '템플릿');
  
  // 파일 다운로드
  XLSX.writeFile(workbook, 'qr_code_template.xlsx');
};

// 엑셀 파일 처리를 위한 변수들 선언
const excelFile = ref(null);
const excelFileName = ref('');
const parsedExcelRows = ref([]);

// 엑셀 파일 삭제 함수
const removeExcelFile = () => {
  // 엑셀 파일 관련 변수 초기화
  excelFile.value = null;
  excelFileName.value = '';
  parsedExcelRows.value = [];
  qrBatchCreationList.value = [];
  batchGlobalError.value = '';
  batchProgress.value = 0;
  
  // QR 코드 타입을 기본값으로 초기화
  formData.value.qrType = '';
  
  // 타겟 콘텐츠를 빈 값으로 초기화
  formData.value.targetContent = '';
  
  // 파일 입력 요소 초기화
  const fileInput = document.getElementById('excelFileInput');
  if (fileInput) {
    fileInput.value = '';
  }
  
};
const qrBatchCreationList = ref([]);
const isBatchProcessing = ref(false);
const batchProgress = ref(0);
const batchGlobalError = ref('');

// 엑셀에서 추출한 QR 코드 샘플 데이터 (미리보기용)
const excelSampleQrData = ref({
  isActive: false,
  qrType: '',
  targetContent: ''
});

// 엑셀 파일 선택 핸들러
const handleExcelFileSelected = (event) => {
  const file = event.target.files[0];
  if (!file) {
    excelFile.value = null;
    excelFileName.value = '';
    return;
  }
  
  // 파일 확장자 검사
  const fileExt = file.name.split('.').pop().toLowerCase();
  if (!['xlsx', 'xls', 'csv'].includes(fileExt)) {
    alert('지원되는 파일 형식은 .xlsx, .xls, .csv 입니다.');
    event.target.value = ''; // 파일 선택 초기화
    excelFile.value = null;
    excelFileName.value = '';
    return;
  }
  
  excelFile.value = file;
  excelFileName.value = file.name;
  
  // 파일 선택 후 미리보기를 위해 파일 파싱 시작
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const data = e.target.result;
      const workbook = XLSX.read(data, { type: 'binary' });
      
      // 첫 번째 시트 사용
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      
      // 배열로 변환
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      // 데이터가 충분한지 확인
      if (jsonData.length >= 2) {
        // 헤더 행 확인
        const headers = jsonData[0];
        const qrTypeColumnIndex = headers.findIndex(header => 
          typeof header === 'string' && header.trim().toLowerCase() === 'qr코드 타입');
        const targetContentColumnIndex = headers.findIndex(header => 
          typeof header === 'string' && header.trim().toLowerCase() === '타겟 콘텐츠');
          
        if (qrTypeColumnIndex !== -1 && targetContentColumnIndex !== -1) {
          // 첫 번째 데이터 행을 사용하여 QR 코드 미리보기 생성
          const firstDataRow = jsonData[1];
          if (firstDataRow && firstDataRow[qrTypeColumnIndex] && firstDataRow[targetContentColumnIndex]) {
            // 엑셀 샘플 데이터 저장
            excelSampleQrData.value.isActive = true;
            excelSampleQrData.value.qrType = firstDataRow[qrTypeColumnIndex];
            excelSampleQrData.value.targetContent = firstDataRow[targetContentColumnIndex];
            
            // 임시로 QR 코드 타입과 타겟 콘텐츠 설정
            const originalType = formData.value.qrType;
            const originalContent = formData.value.targetContent;
            
            // 미리보기를 위해 임시로 값 설정
            formData.value.qrType = excelSampleQrData.value.qrType;
            formData.value.targetContent = excelSampleQrData.value.targetContent;
            
            // QR 코드 미리보기 생성
            generateClientSideQrCode();
            
            // 기존 값 복원 (폼 입력은 유지하면서 보이는 미리보기만 샘플 데이터 사용)
            formData.value.qrType = originalType;
            formData.value.targetContent = originalContent;
          }
        }
      }
    } catch (error) {
      console.error('엑셀 파일 파싱 오류:', error);
      // 파싱 오류 발생 시 아무런 조치를 취하지 않음 (잘못된 파일이거나 파싱 문제가 있을 수 있기 때문에 사용자에게 오류 알림은 제공하지 않음)
    }
  };
  
  reader.onerror = (error) => {
    console.error('파일 읽기 오류:', error);
  };
  
  reader.readAsBinaryString(file);
};

// 엑셀 파일 파싱 함수
const parseExcelFile = () => {
  return new Promise((resolve, reject) => {
    if (!excelFile.value) {
      reject('엑셀 파일이 선택되지 않았습니다.');
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target.result;
        const workbook = XLSX.read(data, { type: 'binary' });
        
        // 첫 번째 시트 사용
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // 배열로 변환
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        // 헤더와 데이터 확인
        if (jsonData.length < 2) {
          reject('엑셀 파일에 데이터가 충분하지 않습니다.');
          return;
        }
        
        // 헤더 행 확인
        const headers = jsonData[0];
        const qrTypeColumnIndex = headers.findIndex(header => 
          typeof header === 'string' && header.trim().toLowerCase() === 'qr코드 타입');
        const targetContentColumnIndex = headers.findIndex(header => 
          typeof header === 'string' && header.trim().toLowerCase() === '타겟 콘텐츠');
          
        if (qrTypeColumnIndex === -1 || targetContentColumnIndex === -1) {
          reject('엑셀 파일에 필요한 열이 없습니다. "QR코드 타입"과 "타겟 콘텐츠" 열이 필요합니다.');
          return;
        }
        
        // 데이터 행 파싱 (최대 100개)
        const dataRows = jsonData.slice(1).slice(0, 100);
        const parsedRows = dataRows.map((row, index) => {
          return {
            originalRowNumber: index + 2, // 엑셀 행 번호 (1부터 시작, 헤더가 1행이므로 2부터)
            qrType: row[qrTypeColumnIndex] || '',
            targetContent: row[targetContentColumnIndex] || ''
          };
        });
        
        // 유효한 데이터가 있는 행만 필터링
        const validRows = parsedRows.filter(row => row.qrType && row.targetContent);
        
        if (validRows.length === 0) {
          reject('엑셀 파일에 유효한 데이터가 없습니다.');
          return;
        }
        
        resolve(validRows);
      } catch (error) {
        console.error('엑셀 파일 파싱 오류:', error);
        reject(`엑셀 파일 파싱 중 오류가 발생했습니다: ${error.message || '알 수 없는 오류'}`);
      }
    };
    
    reader.onerror = (error) => {
      console.error('파일 읽기 오류:', error);
      reject('파일을 읽는 중 오류가 발생했습니다.');
    };
    
    reader.readAsBinaryString(excelFile.value);
  });
};

// 개별 QR 코드 생성 처리 함수
const processSingleQrFromExcelRow = async (excelRowData, index, totalItems, batchDesignOptions) => {
  const qrListItem = qrBatchCreationList.value[index];
  if (!qrListItem) return;
  
  // 상태 업데이트: 처리 중
  qrListItem.status = 'processing';
  
  try {
    // 현재 폼 데이터 복제 (깊은 복사는 필요한 경우 구현)
    const qrSubmitData = JSON.parse(JSON.stringify(formData.value));
    
    // 엑셀에서 가져온 데이터로 타입과 타겟 콘텐츠 덮어쓰기
    qrSubmitData.qrType = excelRowData.qrType;
    qrSubmitData.targetContent = excelRowData.targetContent;
    
    // QR 코드 이름: 기본 이름 + 순번
    qrSubmitData.qrName = `${formData.value.qrName}-${index + 1}`;
    qrListItem.finalQrName = qrSubmitData.qrName;
    
    // FormData 객체 생성
    const formDataObj = new FormData();
    
    // 모든 폼 데이터를 FormData에 추가
    Object.keys(qrSubmitData).forEach(key => {
      if (qrSubmitData[key] !== null && qrSubmitData[key] !== undefined) {
        // 날짜는 문자열로 변환
        if (key === 'validFromDate' || key === 'validToDate') {
          const serverDate = formatDateForServer(qrSubmitData[key]);
          if (serverDate) { // null이 아닐 경우에만 추가
             formDataObj.append(key, serverDate);
          }
        } else if (key === 'qrType' && qrSubmitData[key] === 'EVENT_ATTENDANCE') {
          // QR 코드 타입이 '이벤트'일 경우 'EVENT'로 변경하여 전송
          formDataObj.append(key, 'EVENT');
        } else {
          formDataObj.append(key, qrSubmitData[key]);
        }
      }
    });
    
    // 프로젝트 ID 추가 (필요한 경우)
    if (typeof currentProject !== 'undefined' && currentProject?.value?.projectId) {
      formDataObj.append('projectId', currentProject.value.projectId);
    }
    
    // QR 코드 로고 이미지가 있는 경우 추가
    if (logoFile.value) {
      formDataObj.append('logoImageFile', logoFile.value);
    }
    
    // 위치 이미지가 있는 경우 추가
    if (locationImageFile.value) {
      formDataObj.append('qrInstalledImageFile', locationImageFile.value);
    }
    
    // 위치 정보가 있는 경우 추가
    if (locationData.value.latitude && locationData.value.longitude) {
      formDataObj.append('installationLocationLat', locationData.value.latitude);
      formDataObj.append('installationLocationLng', locationData.value.longitude);
      formDataObj.append('installationLocation', locationData.value.address || '');
    }
    
    // 디자인 옵션 추가 (JSON 문자열로 변환)
    // 인자로 전달된 배치용 디자인 옵션 사용
    let designOptionsJSON = '{}';
    if (batchDesignOptions) { // 전달받은 고정된 디자인 옵션 사용
      // 단일 생성과 동일한 구조로 변경
      designOptionsJSON = JSON.stringify({
        dotsOptions: {
          color: batchDesignOptions.dotsOptions?.color || '#000000',
          type: 'square',
          cornersSquareOptions: {
            color: batchDesignOptions.dotsOptions?.cornersSquareOptions?.color || '#000000',
            type: batchDesignOptions.dotsOptions?.cornersSquareOptions?.type || 'square'
          },
          cornersDotOptions: {
            color: batchDesignOptions.dotsOptions?.cornersDotOptions?.color || '#000000',
            type: batchDesignOptions.dotsOptions?.cornersDotOptions?.type || 'square'
          }
        },
        backgroundOptions: {
          color: batchDesignOptions.backgroundOptions?.color || '#FFFFFF'
        },
        ...(batchDesignOptions.logoRatio && { logoRatio: batchDesignOptions.logoRatio }),
        errorCorrectionLevel: batchDesignOptions.errorCorrectionLevel || 'M',
        layoutOptions: null
      });
    } else if (typeof designOptions !== 'undefined' && designOptions.value) { // 후방 호환성 유지
      // 단일 생성과 동일한 구조로 변경
      designOptionsJSON = JSON.stringify({
        dotsOptions: {
          color: designOptions.value.dotsOptions?.color || '#000000',
          type: 'square',
          cornersSquareOptions: {
            color: designOptions.value.dotsOptions?.cornersSquareOptions?.color || '#000000',
            type: designOptions.value.dotsOptions?.cornersSquareOptions?.type || 'square'
          },
          cornersDotOptions: {
            color: designOptions.value.dotsOptions?.cornersDotOptions?.color || '#000000',
            type: designOptions.value.dotsOptions?.cornersDotOptions?.type || 'square'
          }
        },
        backgroundOptions: {
          color: designOptions.value.backgroundOptions?.color || '#FFFFFF'
        },
        ...(designOptions.value.logoRatio && { logoRatio: designOptions.value.logoRatio }),
        errorCorrectionLevel: designOptions.value.errorCorrectionLevel || 'M',
        layoutOptions: null
      });
    }
    formDataObj.append('designOptions', designOptionsJSON);
    
    // QR 코드 생성 API 호출
    const response = await createQrCode(formDataObj);
    
    // 성공 처리
    qrListItem.status = 'success';
    qrListItem.generatedQrData = response.data;
  } catch (error) {
    // 오류 처리
    console.error(`QR 코드 생성 실패 (${qrListItem.finalQrName}):`, error);
    qrListItem.status = 'error';
    qrListItem.errorMessage = error.response?.data?.message || error.message || '알 수 없는 오류가 발생했습니다.';
  }
  
  // 진행률 업데이트
  batchProgress.value = ((index + 1) / totalItems) * 100;
};

// 화면 아래로 스크롤하는 함수
const scrollToBottom = () => {
  setTimeout(() => {
        // 배치 결과 영역 요소 찾기
    const batchResultsElement = document.querySelector('.qr-batch-results');
    if (batchResultsElement) {
      batchResultsElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
      // 요소가 없는 경우 대체 스크롤 방법
      try {
        const formActions = document.querySelector('.form-actions');
        if (formActions) {
          formActions.scrollIntoView({ behavior: 'smooth', block: 'end' });
        } else {
          // 이 중 어느 것도 작동하지 않을 경우 최종 방법
          const scrollingElement = document.scrollingElement || document.documentElement;
          scrollingElement.scrollTop = scrollingElement.scrollHeight;
        }
      } catch (e) {
        console.error('스크롤 오류:', e);
      }
    }
  }, 500);
};

// 연속 생성 시작 함수
const startBatchCreation = async () => {
  if (!excelFile.value || isBatchProcessing.value) {
    return;
  }
  
  isBatchProcessing.value = true;
  batchProgress.value = 0;
  batchGlobalError.value = '';
  
  // 항상 배열로 초기화
  qrBatchCreationList.value = [];  // 기존 배열 초기화
  
  try {
    // 엑셀 파일 파싱
    const validRows = await parseExcelFile();
    parsedExcelRows.value = validRows;
    
    // 파싱된 데이터에서 QR코드 생성 목록 초기화
    qrBatchCreationList.value = validRows.map((row, index) => ({
      id: Date.now() + index, // 고유 ID
      excelRowNumber: row.originalRowNumber,
      finalQrName: `${formData.value.qrName}-${index + 1}`,
      qrType: row.qrType,
      targetContent: row.targetContent,
      status: 'pending',
      errorMessage: '',
      generatedQrData: null
    }));
    
    // 현재 UI에 설정된 디자인 옵션들을 가져와 생성 (단일 QR 코드 등록과 동일한 정보 구조 사용)
    const currentDesignOptions = {
      dotsOptions: {
        color: qrColor.value || '#000000', 
        type: 'square',
        cornersSquareOptions: {
          color: qrEyeColor.value || '#000000',
          type: qrEyeStyle.value === 0 ? 'square' : 'dot'
        },
        cornersDotOptions: {
          color: qrEyeColor.value || '#000000',
          type: qrEyeStyle.value === 0 ? 'square' : 'dot'
        }
      },
      backgroundOptions: {
        color: qrBgColor.value || '#FFFFFF'
      },
      ...(logoPreview.value && { logoRatio: logoSize.value ? (logoSize.value / 100) : 0.4 }),
      errorCorrectionLevel: qrErrorCorrectionLevel.value, // 오류 복원 수준 포함
      layoutOptions: null
    };
    
    // 각 행에 대해 순차적으로 QR 코드 생성 처리
    for (let i = 0; i < validRows.length; i++) {
      await processSingleQrFromExcelRow(validRows[i], i, validRows.length, currentDesignOptions);
    }
  } catch (error) {
    console.error('연속 생성 중 오류 발생:', error);
    batchGlobalError.value = typeof error === 'string' ? error : (error.message || '연속 생성 중 오류가 발생했습니다.');
  } finally {
    isBatchProcessing.value = false;
    // 최종 진행률 100%로 설정 (중간에 오류가 발생했더라도)
    batchProgress.value = 100;
  }
};

// 디자인 옵션 초기화 함수
const resetDesignOptions = () => {
  qrColor.value = '#000000';
  qrBgColor.value = '#ffffff';
  qrEyeColor.value = '#000000';
  qrDotsStyle.value = 0; // 네모
  qrEyeStyle.value = 0; // 네모
  qrErrorCorrectionLevel.value = 'M'; // 오류 복원 수준 초기화 (중간)

  // 로고 관련 상태 초기화
  logoPreview.value = '';
  logoFile.value = null;
  logoSize.value = 40; // 원본 이미지 크기의 40%로 초기화
  if (logoInput.value) {
    logoInput.value.value = ''; // 파일 입력 필드 초기화
  }
  
  // A4 캔버스 및 배경 이미지 관련 상태 초기화
  useA4Canvas.value = false;
  useBackgroundImage.value = false;
  backgroundImagePreview.value = null;
  backgroundImageFile.value = null;
  backgroundFitMode.value = 'fit'; // 기본 맞춤 모드 초기화
  if (backgroundInput.value) {
    backgroundInput.value.value = ''; // 배경 이미지 파일 입력 필드 초기화
  }

  // QR 코드 위치 및 크기 초기화
  qrPositionX.value = 50;
  qrPositionY.value = 50;
  qrWidth.value = 100;
  qrHeight.value = 100;

  // 변경된 디자인 옵션을 즉시 미리보기에 반영하기 위해 디바운스 함수 호출
  // (단, 타겟 콘텐츠가 있어야 의미가 있음)
  if (isQrCodeGenerated.value || (formData.value.targetContent && formData.value.targetContent.trim() !== '')) {
     // generateQrCodeDebounced(); // 디바운스 대신 즉시 반영하도록 변경 가능
     isDesignUpdateOnly.value = true;
     generateClientSideQrCode().finally(() => {
       isDesignUpdateOnly.value = false;
     });
  }
};


// 폼 제출 처리
const handleSubmit = async () => {
  isSubmitting.value = true;
  error.value = '';

  // 엑셀 파일이 업로드된 경우 연속 생성 실행
  if (excelFile.value && !isEditMode.value) {
    // QR코드 이름만 필수로 검사
    if (!formData.value.qrName) {
      error.value = 'QR 코드 이름은 필수입니다.';
      isSubmitting.value = false;
      return;
    }
    // 스크롤 함수 바로 호출 (배치 생성 완료를 기다리지 않음)
    scrollToBottom();
    
    // 배치 생성을 비동기로 시작
    startBatchCreation().finally(() => {
      isSubmitting.value = false;
    });

    return;
  }

  // 엑셀 파일이 업로드되지 않은 경우 단일 생성 처리 진행 (기존 부분)
  // 유효성 검사 (예: 필수 필드)
  if (!formData.value.qrName || !formData.value.qrType || !formData.value.status) {
      error.value = '필수 필드를 모두 입력해주세요.';
      isSubmitting.value = false;
      return;
  }

  // 랜딩 페이지 타입일 때 유효성 검사
  if (formData.value.qrType === 'LANDING_PAGE') {
    if (!selectedLandingPageId.value) {
      error.value = '랜딩 페이지를 선택해주세요.';
      isSubmitting.value = false;
      return;
    }
  } else if (formData.value.qrType === 'EVENT_ATTENDANCE') {
    if (!selectedEventId.value) {
      error.value = '이벤트를 선택해주세요.';
      isSubmitting.value = false;
      return;
    }
    // 이벤트 타입 targetContent URL 설정
    formData.value.targetContent = `${getFrontendDomain()}/event/${selectedEventId.value}`;

    // 이벤트 ID 설정 (formData 객체에는 linkedEventId만 설정)
    formData.value.linkedEventId = selectedEventId.value;

  } else if (formData.value.qrType === 'LOCATION') {
    // 위치 타입일 때 유효성 검사
    if (!formData.value.targetContent) {
      // 위치 정보가 없는 경우, 타겟 위치 정보로 타겟 콘텐츠 설정
      if (targetLocationData.value.latitude && targetLocationData.value.longitude) {
        formData.value.targetContent = `https://maps.google.com/?q=${targetLocationData.value.latitude},${targetLocationData.value.longitude}`;
      } else {
        // 수정 모드에서는 기본값 설정
        if (isEditMode.value) {
          const defaultLat = '37.5665';
          const defaultLng = '126.9780';
          targetLocationData.value.latitude = defaultLat;
          targetLocationData.value.longitude = defaultLng;
          targetLocationData.value.address = '서울시청 (기본값)';

          formData.value.targetContent = `https://maps.google.com/?q=${defaultLat},${defaultLng}`;
        } else {
          error.value = '위치 정보를 선택해주세요.';
          isSubmitting.value = false;
          return;
        }
      }
    }

    // 타겟 콘텐츠 형식 확인 및 수정
    if (!formData.value.targetContent.startsWith('https://maps.google.com/')) {
      // Google Maps URL 형식이 아닌 경우 형식 수정
      if (targetLocationData.value.latitude && targetLocationData.value.longitude) {
        formData.value.targetContent = `https://maps.google.com/?q=${targetLocationData.value.latitude},${targetLocationData.value.longitude}`;
      }
    }
  } else if (formData.value.qrType === 'WIFI') {
    // Wi-Fi 타입일 때 유효성 검사
    if (!formData.value.targetContent) {
      // Wi-Fi 정보가 없는 경우, Wi-Fi 정보로 타겟 콘텐츠 설정
      if (wifiData.value.ssid) {
        formData.value.targetContent = generateWifiQrContent();
      } else {
        // 수정 모드에서는 기본값 설정
        if (isEditMode.value) {
          wifiData.value = {
            ssid: 'Wi-Fi 네트워크',
            securityType: 'WPA',
            password: 'password',
            hidden: false
          };

          formData.value.targetContent = generateWifiQrContent();
        } else {
          error.value = 'Wi-Fi 네트워크 이름(SSID)을 입력해주세요.';
          isSubmitting.value = false;
          return;
        }
      }
    }

    // 보안 유형이 있는데 비밀번호가 없는 경우 오류
    if (wifiData.value.securityType && !wifiData.value.password) {
      error.value = 'Wi-Fi 비밀번호를 입력해주세요.';
      isSubmitting.value = false;
      return;
    }

    // 타겟 콘텐츠 형식 확인 및 수정
    if (!formData.value.targetContent.startsWith('WIFI:')) {
      // Wi-Fi 형식이 아닌 경우 형식 수정
      if (wifiData.value.ssid) {
        formData.value.targetContent = generateWifiQrContent();
      }
    }
  } else if (!formData.value.targetContent) {
    // 랜딩 페이지, 이벤트, 위치가 아닌 다른 타입의 경우 타겟 콘텐츠 필수
    error.value = '타겟 콘텐츠를 입력해주세요.';
    isSubmitting.value = false;
    return;
  }

  // URL 유효성 검사
   if ((formData.value.qrType === 'URL' || formData.value.qrType === 'SNS_LINK') && !isValidTargetContent.value) {
     error.value = targetContentErrorMessage.value;
     isSubmitting.value = false;
     return;
   }

   // 날짜 유효성 검사 (선택적) - 종료일이 시작일보다 빠른 경우 등
   if (formData.value.validFromDate && formData.value.validToDate) {
       const fromDate = new Date(formData.value.validFromDate);
       const toDate = new Date(formData.value.validToDate);
       if (toDate < fromDate) {
           error.value = '유효 종료일은 시작일보다 빠를 수 없습니다.';
           isSubmitting.value = false;
           return;
       }
   }

  // A4 캔버스 고화질 이미지 생성 (A4 박스 사용 시에만)
  let a4CanvasImageBase64 = null;
  if (useA4Canvas.value) {
    try {
      const a4CanvasImage = await generateA4CanvasImage();
      if (a4CanvasImage) {
        // File 객체를 Base64로 변환
        a4CanvasImageBase64 = await new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target.result);
          reader.readAsDataURL(a4CanvasImage);
        });
      }
    } catch (canvasError) {
      console.warn('A4 캔버스 이미지 생성 실패:', canvasError);
      // 이미지 생성 실패해도 QR 코드 생성은 계속 진행
    }
  }

  // 현재 디자인 옵션을 JSON 문자열로 조합 (서버 요구 형식)
  const currentDesignOptions = JSON.stringify({
    dotsOptions: {
      color: qrColor.value, // 사용자가 선택한 QR 코드 색상 사용
      type: 'square',
      cornersSquareOptions: {
        color: qrEyeColor.value, // 사용자가 선택한 눈 색상 사용
        type: qrEyeStyle.value === 0 ? 'square' : 'dot' // 사용자가 선택한 눈 모양 사용
      },
      cornersDotOptions: {
        color: qrEyeColor.value, // 사용자가 선택한 눈 색상 사용
        type: qrEyeStyle.value === 0 ? 'square' : 'dot' // 사용자가 선택한 눈 모양 사용
      }
    },
    backgroundOptions: {
      color: qrBgColor.value // 사용자가 선택한 배경 색상 사용
    },
    // 로고 비율은 로고가 있을 때만 포함
    ...(logoPreview.value && { logoRatio: logoSize.value / 100 }),
    // A4 캔버스 및 배경 이미지 사용 여부 추가
    useA4Canvas: useA4Canvas.value,
    useBackgroundImage: useBackgroundImage.value,
    // A4 사이즈 정보 추가 (HTML A4 캔버스의 실제 픽셀 크기)
    a4Size: useA4Canvas.value ? (() => {
      const containerRect = a4CanvasContainer.value?.getBoundingClientRect();
      return {
        width: containerRect?.width || 0,
        height: containerRect?.height || 0
      };
    })() : null,
    // QR 코드 위치 및 크기 정보 (A4 캔버스 사용 시 mm 단위, 미사용 시 픽셀 단위)
    qrLayout: useA4Canvas.value ? {
      positionX: mmPositionX.value,
      positionY: mmPositionY.value,
      width: mmWidth.value,
      height: mmHeight.value,
      unit: 'mm'
    } : {
      positionX: qrPositionX.value,
      positionY: qrPositionY.value,
      width: qrWidth.value,
      height: qrHeight.value,
      unit: 'px'
    },
    // 배경 이미지 맞춤 모드
    backgroundFitMode: useBackgroundImage.value ? backgroundFitMode.value : null,
    // A4 캔버스 이미지 (Base64 형태로 포함)
    ...(a4CanvasImageBase64 && { a4CanvasImage: a4CanvasImageBase64 }),
    errorCorrectionLevel: qrErrorCorrectionLevel.value, // 사용자가 선택한 오류 복원 수준 사용
    layoutOptions: null
  });

  // 현재 프로젝트 ID 확인
  if (!currentProject.value || !currentProject.value.projectId) {
    error.value = '프로젝트 ID를 찾을 수 없습니다. 프로젝트가 선택되어 있는지 확인해주세요.';
    isSubmitting.value = false;
    return;
  }

  // FormData 생성 (파일 업로드 고려)
  const submissionData = new FormData();

  // 폼 데이터 추가
  Object.keys(formData.value).forEach(key => {
    if (key === 'validFromDate' || key === 'validToDate') {
      // 날짜 형식 변환 후 추가
      const serverDate = formatDateForServer(formData.value[key]);
      if (serverDate) { // null이 아닐 경우에만 추가
         submissionData.append(key, serverDate);
      }
    } else if (key === 'qrType' && formData.value[key] === 'EVENT_ATTENDANCE') {
      // QR 코드 타입이 '이벤트'일 경우 'EVENT'로 변경하여 전송
      submissionData.append(key, 'EVENT');
    } else if (formData.value[key] !== null && formData.value[key] !== undefined) {
      // null 이나 undefined 아닌 값만 추가
      submissionData.append(key, formData.value[key]);
    }
  });

  // 프로젝트 ID 추가
  submissionData.append('projectId', currentProject.value.projectId);

  // 기존 FormData 객체를 유지하면서 위치 정보 필드 제거 및 추가

  // 위치 정보 관련 필드 목록
  const locationFields = [
    'installationLocationLat', 'installationLocationLng', 'installationLocation',
    'installation_location_lat', 'installation_location_lng', 'installation_location',
    'latitude', 'longitude', 'locationAddress'
  ];

  // 위치 정보 필드 제거를 위해 현재 FormData의 모든 키 수집
  const allKeys = [];
  for (let pair of submissionData.entries()) {
    allKeys.push(pair[0]);
  }

  // 위치 정보 관련 필드 제거
  for (let key of allKeys) {
    if (locationFields.includes(key)) {
      // FormData에서는 delete 메서드가 없으므로 같은 키로 빈 값을 설정하여 "제거"
      // 실제로는 서버에서 빈 문자열로 처리됨
      submissionData.set(key, '');
    }
  }

  // 위치 정보 추가 (있는 경우)
  if (locationData.value.latitude && locationData.value.longitude) {
    // 단일 필드명으로만 위치 정보 추가
    submissionData.set('installationLocationLat', locationData.value.latitude);
    submissionData.set('installationLocationLng', locationData.value.longitude);
    submissionData.set('installationLocation', locationData.value.address || '');
    
  }
  
  // 위치 정보와 상관없이 이미지 파일 처리
  if (locationImageFile.value) {
    submissionData.set('qrInstalledImageFile', locationImageFile.value);
  } 

  // 디자인 옵션 JSON 문자열 추가 (수정 모드에서도 포함)
  // set 메서드를 사용하여 중복 방지
  submissionData.set('designOptions', currentDesignOptions);

  // 로고 파일 처리 (생성 모드와 수정 모드 모두)
  if (logoFile.value) {
    submissionData.set('logoImageFile', logoFile.value);
  }
  
  // 배경 이미지 파일 처리 (배경 이미지 토글이 활성화된 경우에만)
  if (useBackgroundImage.value && backgroundImageFile.value) {
    submissionData.set('backgroundImageFile', backgroundImageFile.value);
    // 배경 이미지 맞춤 모드 정보 추가
    submissionData.set('backgroundFitMode', backgroundFitMode.value);
  }

  // A4 캔버스 이미지는 이제 designOptions 안에 포함됨 

  // 이벤트 타입일 때 이벤트 ID 추가
  if (formData.value.qrType === 'EVENT_ATTENDANCE') {
    // 이벤트 ID 관련 필드 제거 (중복 방지)
    const eventIdFields = ['linkedEventId', 'linked_event_id', 'eventId', 'event_id'];

    // 이벤트 ID 관련 필드 제거
    for (let key of eventIdFields) {
      // 기존 값을 빈 문자열로 설정하여 "제거"
      submissionData.set(key, '');
    }

    // 서버에서 사용하는 필드명으로 이벤트 ID 추가 (단일 필드명으로만 추가)
    submissionData.set('linkedEventId', selectedEventId.value);

    // formData 객체에도 linkedEventId 설정
    formData.value.linkedEventId = selectedEventId.value;

  }

  try {
    let response;
    if (isEditMode.value) {
      // 업데이트 API 호출
      response = await updateQrCode(route.params.qrCodeId, submissionData);
      // 성공 알림 또는 목록 페이지로 리디렉션 등
      alert('QR 코드가 성공적으로 수정되었습니다.'); // 임시 알림
    } else {
      // 생성 API 호출
      response = await createQrCode(submissionData);
      // 성공 알림 또는 목록 페이지로 리디렉션 등
       alert('QR 코드가 성공적으로 생성되었습니다.'); // 임시 알림
    }
    router.push({ name: 'qr-list' }); // 성공 시 목록 페이지로 이동
  } catch (err) {
    console.error(`QR 코드 ${isEditMode.value ? '수정' : '생성'} 실패:`, err);
    // 서버 응답에서 오류 메시지 추출 시도
    const errorMessage = err.response?.data?.message || err.message || '알 수 없는 오류가 발생했습니다.';
    error.value = `QR 코드 처리 중 오류 발생: ${errorMessage}`;
  } finally {
    isSubmitting.value = false;
  }
};

</script>
<style scoped>
.qr-code-form {
  padding: 20px;
  max-width: 900px; /* 폼 너비 증가 */
  margin: 0 auto; /* 가운데 정렬 */
}

/* 위치 입력 폼 스타일 */
.location-inputs, .location-target-inputs {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.location-input-group {
  flex: 1;
}

.location-input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
  color: #555;
}

.location-address {
  margin-bottom: 15px;
}

.location-address label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
  color: #555;
}

.map-select-button {
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 10px;
  min-height: 52px;
}

.map-select-button:hover {
  background-color: #3367D6;
}

.map-select-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* QR 코드 설치 위치와 타겟 위치 구분 스타일 */
.location-target-inputs {
  background-color: #f0f7ff;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #d0e3ff;
}

.location-target-button {
  background-color: #34A853;
}

.location-target-button:hover {
  background-color: #2E8B57;
}

/* Wi-Fi 입력 폼 스타일 */
.wifi-inputs {
  background-color: #f0fff0;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #d0ffd0;
  margin-bottom: 15px;
}

.wifi-input-group {
  margin-bottom: 15px;
}

.wifi-input-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
  color: #555;
}

.wifi-input-group input,
.wifi-input-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px; /* 폰트 크기 */
  box-sizing: border-box;
}

.password-input-container {
  position: relative;
  display: flex;
}

.password-input-container input {
  flex: 1;
  border-radius: 4px 0 0 4px;
}

.toggle-password-button {
  padding: 0 15px;
  background-color: #f1f1f1;
  border: 1px solid #ddd;
  border-left: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
  font-size: 10px; /* 아이콘 크기 조정 */
  line-height: 18px; /* 아이콘 수직 정렬 */
  transition: background-color 0.2s;
}
.toggle-password-button:hover {
  background-color: #e0e0e0;
}

.wifi-checkbox-group {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.wifi-checkbox-group input[type="checkbox"] {
  margin-right: 10px;
}

.target-content-preview {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.target-content-preview label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.preview-content {
  font-family: monospace;
  word-break: break-all;
}

.form-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 30px; /* 패딩 증가 */
  margin-top: 20px;
  display: grid; /* Grid 레이아웃 사용 */
  gap: 20px 30px;
}
/* 모바일 화면 등 작은 화면에서는 1열로 변경 */
@media (max-width: 768px) {
  .form-container {
    grid-template-columns: 1fr;
  }
}

/* 폼 그룹 스타일 */
.form-group {
  margin-bottom: 0; /* Grid gap으로 간격 조절하므로 제거 */
  /* 특정 그룹이 2열을 차지하도록 설정 */
}
.form-group:nth-child(3), /* 타겟 콘텐츠 */
.form-group:nth-child(7), /* 설명 */
.form-group:nth-child(8), /* 디자인 옵션 */
.qr-code-preview, /* QR 코드 미리보기 */
.form-actions, /* 액션 버튼 */
.error-message /* 오류 메시지 */
{
  grid-column: 1 / -1; /* 1열부터 마지막 열까지 차지 */
}


.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

.required {
  color: #f44336; /* Red color for required fields */
  margin-left: 4px;
}

.form-group input[type="text"],
.form-group select,
.form-group textarea,
.form-group input[type="datetime-local"] {
  width: 100%;
  padding: 12px; /* 입력 필드 패딩 증가 */
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px; /* 폰트 크기 */
  box-sizing: border-box;
}

.form-group textarea {
  resize: vertical; /* Allow vertical resizing */
}

/* Date group styling */
.date-group {
  width: 100%;
}

/* Form actions alignment */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px; /* 위쪽 간격 조정 */
  padding-top: 20px;
  border-top: 1px solid #eee; /* 구분선 추가 */
}

.cancel-btn, .submit-btn {
  padding: 12px 24px; /* 버튼 크기 증가 */
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px; /* 버튼 폰트 크기 */
}

.cancel-btn {
  background-color: #e0e0e0;
  color: #333;
}
.cancel-btn:hover {
  background-color: #d0d0d0;
}

.submit-btn {
  background-color: #4CAF50;
  color: white;
}
.submit-btn:hover:not(:disabled) {
  background-color: #45a049;
}
.submit-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Loading and Error Messages */
.loading, .error-message {
  padding: 15px; /* 패딩 조정 */
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-top: 20px;
  grid-column: 1 / -1; /* 전체 너비 차지 */
}

.error-message {
  color: #f44336;
  background-color: #ffebee;
  border: 1px solid #f44336;
  font-weight: bold;
}

.loading-indicator {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  text-align: center;
  color: #666;
  font-style: italic;
  margin-bottom: 10px;
}

.field-note.error {
  color: #f44336;
  font-weight: bold;
}

.field-note {
  margin-top: 6px;
  font-size: 13px; /* 약간 작게 */
  color: #555; /* 기본 색상 */
  font-style: italic;
}
/* 오류 관련 노트는 다른 색상 */
.form-group .field-note:has(+ .input-error), /* URL 오류 등 */
.field-note.error { /* 명시적 에러 클래스 */
  color: #f44336;
}
.form-group:has(.input-error) .field-note { /* 입력 필드 오류 시 노트 색상 */
   color: #f44336;
}

.input-error {
  border-color: #f44336 !important;
  background-color: #fff0f0; /* 약간의 배경색 */
}

/* 이벤트 선택 관련 스타일 */

.selected-event-info {
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #e8f5e9;
  border-left: 3px solid #4CAF50;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.selected-event-label {
  font-weight: bold;
  margin-right: 8px;
  color: #2e7d32;
}

.selected-event-value {
  color: #1b5e20;
  font-weight: 500;
}


/* QR Code Preview Styles */
.qr-code-preview {
  background-color: #ffffff; /* 흰색 배경 */
  border: 1px solid #e0e0e0; /* 경계선 추가 */
  border-radius: 8px;
  padding: 25px; /* 패딩 증가 */
  margin-top: 10px; /* 위쪽 간격 조정 */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code-preview h3 {
  margin-top: 0;
  color: #333;
  margin-bottom: 20px; /* 제목 아래 간격 */
}

.qr-preview-container {
  width: 300px;
  height: 300px;
  margin: 0 auto 20px;
  position: relative;
  border: 1px dashed #ccc; /* 테두리가 확대 시 잘릴 수 있으므로, 필요 없다면 제거하거나 조정 */
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  /* 중요: overflow: hidden 으로 설정되어 있는지 확인 */
  overflow: hidden;
  padding: 0;
  background-color: #f8f8f8;
}

/* [수정] 생성된 SVG/Canvas 요소가 컨테이너를 꽉 채우도록 강제 */
.qr-preview-container > canvas,
.qr-preview-container > svg {
  display: block; /* 인라인 공백 제거 */
  width: 100% !important; /* 컨테이너 너비에 맞춤 */
  height: 100% !important; /* 컨테이너 높이에 맞춤 */
  margin: 0 !important; /* 라이브러리 기본 마진 무시 */
  padding: 0 !important; /* 라이브러리 기본 패딩 무시 */
  object-fit: contain; /* 비율 유지하며 채우기 */
  transform: scale(1.04); /* 예시 값, 1.0 ~ 1.1 사이에서 조정해보세요 */
  transform-origin: center center; /* 확대 기준점을 중앙으로 설정 */
}

.image-placeholder {
  width: 100%; /* 부모 너비 채우기 */
  display: flex;
  justify-content: center; /* 가운데 정렬 */
  align-items: center;
  color: #999;
  font-size: 14px;
  text-align: center;
  padding: 10px;
  box-sizing: border-box;
}

/* QR Code Scan Reliability Styles */
.scan-reliability-container {
  margin-top: 20px; /* 위쪽 간격 */
  padding: 15px; /* 내부 패딩 */
  border-radius: 6px; /* 둥근 모서리 */
  background-color: #f0f0f0; /* 배경색 */
  width: 100%; /* 너비 100% */
  max-width: 350px; /* 최대 너비 제한 */
  box-sizing: border-box;
}

.scan-reliability-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px; /* 아래 간격 */
  font-weight: bold;
  font-size: 14px; /* 폰트 크기 */
}
/* 신뢰도별 색상 클래스 */
.reliability-high { color: #4CAF50; }
.reliability-medium { color: #FFC107; }
.reliability-low { color: #F44336; }
.reliability-critical { color: #9C27B0; font-weight: bold; } /* 강조 */

.scan-reliability-progress-container {
  width: 100%;
  height: 12px; /* 높이 증가 */
  background-color: #e0e0e0;
  border-radius: 6px; /* 둥근 모서리 */
  overflow: hidden;
  margin-bottom: 8px; /* 아래 간격 */
}

.scan-reliability-progress {
  height: 100%;
  border-radius: 6px; /* 둥근 모서리 */
  transition: width 0.5s ease, background-color 0.5s ease; /* 부드러운 전환 */
}

.scan-reliability-note {
  font-size: 13px; /* 폰트 크기 */
  color: #666;
  font-style: italic;
  text-align: center; /* 가운데 정렬 */
}


/* Design Options/* 디자인 옵션 스타일 */
.design-options-container {
  background-color: #f0f0f5; /* 약간 다른 배경색 */
  border-radius: 6px;
  padding: 20px;
  margin-top: 10px; /* 위쪽 간격 */
}

.design-group {
  margin-bottom: 15px;
}

/* 토글 스위치 스타일 */
.toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 1px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: #4285F4;
}

.toggle-switch input:checked + label:before {
  transform: translateX(26px);
}

/* 배경 이미지 업로드 스타일 */
.background-image-container {
  margin-top: 10px;
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 4px;
}

/* A4 캔버스 섹션 스타일 */
.a4-canvas-section {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  width: 100%;
  max-width: 100%; /* 부모 요소를 벗어나지 않도록 */
  box-sizing: border-box; /* 패딩 포함한 크기 계산 */
  overflow: hidden; /* 자식 요소가 벗어나지 않도록 */
}

.a4-canvas-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

/* A4 캔버스 스타일 */
.a4-canvas {
  width: 100%;
  max-width: 100%; /* 부모 요소를 벗어나지 않도록 */
  margin-bottom: 15px;
  box-sizing: border-box; /* 패딩 포함한 크기 계산 */
}

/* A4 캔버스 부모 컨테이너 */
.a4-canvas-parent-container {
  position: relative;
  width: 100%;
  max-width: 100%; /* 부모 요소를 벗어나지 않도록 */
  min-height: 600px; /* 최소 높이 설정 */
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden; /* 자식 요소가 벗어나지 않도록 */
  box-sizing: border-box; /* 패딩 포함한 크기 계산 */
  margin: 0; /* 마진 제거 */
}

.canvas-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 5px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.canvas-toolbar span {
  font-size: 14px;
  margin-right: 10px;
  font-weight: 500;
}

.image-controls {
  display: flex;
  align-items: center;
}

.control-btn {
  padding: 5px 10px;
  margin: 0 5px;
  border: 1px solid #ddd;
  background-color: #fff;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
}

.control-btn:hover {
  background-color: #f0f0f0;
}

.control-btn.active {
  background-color: #4285F4;
  color: white;
  border-color: #4285F4;
}

.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.background-image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.background-preview {
  /* 기본 스타일 */
  max-width: 100%;
  max-height: 100%;
  border: none;
  transition: all 0.3s ease;
}

/* 배경 이미지 맞춤 모드 */
.bg-fill {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bg-fit {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.bg-original {
  max-width: none;
  max-height: none;
  width: auto;
  height: auto;
}

.remove-bg-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  z-index: 10;
}

.background-upload {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px dashed #ccc;
  background-color: #f9f9f9;
  text-align: center;
  cursor: pointer;
}

.background-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 20px;
}

.background-file-input {
  display: none;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 5px;
  .design-options-container {
    grid-template-columns: 1fr;
  }
}

.logo-upload-section, .advanced-options {
  background-color: #fff; /* 각 섹션 배경 흰색 */
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.logo-upload-section h4, .advanced-options h4 {
  margin-top: 0;
  margin-bottom: 15px; /* 제목 아래 간격 */
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.logo-preview-container {
  margin: 15px 0;
  display: flex;
  justify-content: center; /* 가운데 정렬 */
}

.logo-preview {
  position: relative;
  width: 100px; /* 로고 미리보기 크기 조정 */
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-image {
  max-width: 90%; /* 내부 여백 확보 */
  max-height: 90%;
  object-fit: contain;
}

.remove-logo-btn {
  position: absolute;
  top: 3px; /* 위치 조정 */
  right: 3px;
  background-color: rgba(211, 47, 47, 0.8); /* 약간 투명한 빨간색 */
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px; /* 버튼 크기 조정 */
  height: 20px;
  font-size: 10px; /* 아이콘 크기 조정 */
  line-height: 18px; /* 아이콘 수직 정렬 */
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s;
}
.remove-logo-btn:hover {
  background-color: rgba(198, 40, 40, 1); /* 호버 시 진하게 */
}

.logo-upload {
  width: 100px; /* 로고 업로드 영역 크기 조정 */
  height: 100px;
  border: 2px dashed #ccc;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: border-color 0.2s;
}
.logo-upload:hover {
  border-color: #aaa;
}

.logo-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  color: #666;
  font-size: 13px; /* 텍스트 크기 */
}

.upload-icon {
  font-size: 20px; /* 아이콘 크기 */
  margin-bottom: 5px;
}

.logo-file-input {
  display: none; /* 숨김 처리 */
}

.logo-options {
  margin-top: 15px; /* 위쪽 간격 */
}

.logo-size-option {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.logo-size-option label {
  margin-bottom: 8px; /* 레이블 아래 간격 */
  font-size: 14px;
  color: #555;
}

.logo-size-option input[type="range"] {
  width: 100%;
  margin-bottom: 5px;
  cursor: pointer;
}
.logo-size-option span {
   font-size: 13px;
   color: #333;
   text-align: right;
}
.logo-size-option .field-note {
  font-size: 0.8em;
  color: #666;
  margin-top: 4px;
}


.design-group {
  display: grid; /* 내부 요소 정렬 위해 grid */
  grid-template-columns: auto 1fr; /* 레이블 자동, 입력필드 나머지 */
  align-items: center;
  gap: 10px; /* 요소 간 간격 */
  margin-bottom: 15px;
}
/* 오류 복원 수준 그룹은 레이블과 셀렉트만 */
.design-group:has(.error-correction-select) {
  grid-template-columns: auto 1fr;
}
/* 슬라이더 그룹은 레이블, 슬라이더, 값 표시 */
.design-group.slider-group {
    grid-template-columns: auto 1fr auto; /* 레이블, 슬라이더, 값 */
}


.design-group label {
  /* min-width 제거 */
  margin-right: 0; /* grid gap 사용 */
  font-weight: normal;
  color: #555;
  font-size: 14px; /* 폰트 크기 */
  text-align: left; /* 왼쪽 정렬 */
  white-space: nowrap; /* 줄바꿈 방지 */
}

.design-group input[type="color"] {
  width: 40px; /* 색상 피커 크기 조정 */
  height: 30px;
  padding: 0;
  border: 1px solid #ccc;
  cursor: pointer;
  justify-self: start; /* 왼쪽 정렬 */
}

.slider-group input[type="range"] {
  width: 100%; /* 슬라이더 너비 꽉 채움 */
  cursor: pointer;
}

.slider-group span {
  /* width 제거 */
  text-align: right;
  font-size: 14px;
  color: #555;
  white-space: nowrap;
}

.reset-btn {
  margin-top: 15px;
  padding: 8px 16px;
  font-size: 14px;
  background-color: #6c757d; /* 회색 계열 */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  justify-self: start; /* 버튼 왼쪽 정렬 */
}
.reset-btn:hover {
   background-color: #5a6268;
}

.error-correction-select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  grid-column: 2 / 3; /* 입력 필드 위치 */
}

/* QR 코드 버전 정보 스타일 */
.qr-version-info {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  background-color: #e7f3fe; /* 하늘색 배경 */
  margin-bottom: 15px; /* 아래 간격 */
  width: 100%;
  max-width: 350px;
  box-sizing: border-box;
}

.version-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 14px;
}

.version-value {
  color: #0056b3; /* 진한 파란색 */
  font-size: 16px;
}

.size-info {
  color: #555;
  font-size: 13px;
  font-weight: normal;
  margin-left: auto; /* 오른쪽으로 밀기 */
}

.version-note {
  font-size: 13px;
  color: #555;
  font-style: italic;
  text-align: center;
}

/* 반응형 레이아웃 조정 */
@media (max-width: 600px) {
  .form-container {
    grid-template-columns: 1fr; /* 작은 화면에서는 1열로 */
    padding: 20px;
  }
  /* 모든 그룹이 1열을 차지하도록 */
  .form-group,
  .qr-code-preview,
  .form-actions,
  .error-message,
  .design-options-container {
      grid-column: 1 / -1;
  }
  .design-options-container {
     grid-template-columns: 1fr; /* 디자인 옵션 내부도 1열 */
  }
   .date-group {
      width: 100%;
      margin-right: 0;
      margin-bottom: 20px; /* 날짜 그룹 간 간격 */
   }
   .date-group:last-of-type {
       margin-bottom: 0;
   }
   .form-actions {
      justify-content: center; /* 버튼 가운데 정렬 */
   }
}

/* 수정 모드 관련 스타일 */
.disabled-container {
  opacity: 0.7;
  pointer-events: none;
  cursor: not-allowed;
}

.edit-mode-notice {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
  color: #0d6efd;
  text-align: center;
}
/* 위치 사진 업로드 관련 스타일 */
.location-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.location-container {
  margin-top: 10px;
}

.location-inputs {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.location-map-button {
  margin-bottom: 10px;
}

.map-select-button {
  padding: 8px 15px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.map-select-button:hover:not(:disabled) {
  background-color: #45a049;
}

.map-select-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.selected-location {
  margin-top: 8px;
  padding: 8px;
  background-color: #e9f7ef;
  border-radius: 4px;
  font-size: 14px;
}

.location-image-upload {
  margin-top: 15px;
}

.location-image-preview-container {
  margin-top: 10px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  overflow: hidden;
}

.location-image-preview {
  position: relative;
  max-width: 100%;
  max-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.location-image {
  max-width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.remove-image-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.location-image-upload-button {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
}

.location-image-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #555;
}

.location-image-file-input {
  display: none;
}

.upload-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

/* 배경 이미지 컨트롤 스타일 */
.background-image-controls {
  margin-bottom: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.bg-image-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #e8f5e9;
  border-radius: 4px;
  border-left: 3px solid #4CAF50;
}

.bg-image-name {
  font-weight: 500;
  color: #2e7d32;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

.remove-bg-btn-outside {
  background-color: #f8d7da;
  border: 1px solid #f5c2c7;
  color: #842029;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.remove-bg-btn-outside:hover {
  background-color: #f5c2c7;
}

.bg-upload-outside {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.background-upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 25px;
  border: 2px dashed #6c757d;
  border-radius: 6px;
  background-color: #f8f9fa;
  color: #495057;
  cursor: pointer;
  transition: all 0.2s;
}

.background-upload-btn:hover {
  background-color: #e9ecef;
  border-color: #495057;
}

/* A4 캔버스의 배경 이미지 및 QR 코드 이미지 스타일 */
/* 배경 이미지 컨테이너 래퍼 */
.background-container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.background-container-wrapper > * {
  pointer-events: auto;
}

/* QR 코드 컨테이너 래퍼 */
.qr-container-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 50;
  pointer-events: none;
}

.qr-container-wrapper > * {
  pointer-events: auto;
}

/* 크기 조절 가능한 A4 캔버스 컨테이너 스타일 */
.a4-canvas-draggable-container {
  cursor: default;
  z-index: 5 !important;
  border: 3px solid #FF9800;
  border-radius: 8px;
  padding: 0;
  overflow: visible;
  background-color: rgba(255, 152, 0, 0.05);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
  position: relative;
}

/* A4 캔버스 가이드라인 표시 */
.a4-canvas-draggable-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px dashed rgba(255, 152, 0, 0.3);
  pointer-events: none;
  z-index: 1;
}

/* A4 캔버스용 vue-draggable-resizable 핸들 스타일 */
.a4-canvas-draggable-container :deep(.vdr-handle) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background-color: #FF9800 !important;
  border: 2px solid #fff !important;
  z-index: 999 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* 상하 핸들 (tm, bm) - 가로로 긴 직사각형 */
.a4-canvas-draggable-container :deep(.vdr-handle-tm),
.a4-canvas-draggable-container :deep(.vdr-handle-bm) {
  width: 40px !important;
  height: 8px !important;
  border-radius: 4px !important;
  cursor: ns-resize !important;
}

/* 좌우 핸들 (ml, mr) - 세로로 긴 직사각형 */
.a4-canvas-draggable-container :deep(.vdr-handle-ml),
.a4-canvas-draggable-container :deep(.vdr-handle-mr) {
  width: 8px !important;
  height: 40px !important;
  border-radius: 4px !important;
  cursor: ew-resize !important;
}

.a4-canvas-draggable-container :deep(.vdr-handle:hover) {
  background-color: #F57C00 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3) !important;
}

/* 상하 핸들 호버 효과 */
.a4-canvas-draggable-container :deep(.vdr-handle-tm:hover),
.a4-canvas-draggable-container :deep(.vdr-handle-bm:hover) {
  transform: scaleY(1.2) scaleX(1.1) !important;
}

/* 좌우 핸들 호버 효과 */
.a4-canvas-draggable-container :deep(.vdr-handle-ml:hover),
.a4-canvas-draggable-container :deep(.vdr-handle-mr:hover) {
  transform: scaleX(1.2) scaleY(1.1) !important;
}

.a4-canvas-draggable-container :deep(.vdr-stick) {
  z-index: 999 !important;
}

.a4-canvas-draggable-container:hover {
  border-color: #F57C00;
  background-color: rgba(255, 152, 0, 0.1);
  box-shadow: 0 6px 16px rgba(255, 152, 0, 0.3);
}

/* 드래그 가능한 배경 이미지 컨테이너 스타일 */
.background-draggable-container {
  cursor: move;
  z-index: 20 !important;
  border: 2px dashed #4285F4;
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
  background-color: rgba(66, 133, 244, 0.1);
}

.background-draggable-container:hover {
  border-color: #1a73e8;
  background-color: rgba(66, 133, 244, 0.2);
}

/* 드래그 가능한 QR 코드 컨테이너 스타일 */
.qr-draggable-container {
  cursor: move;
  z-index: 100 !important;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 0;
  overflow: hidden;
  min-width: 50px !important;
  min-height: 50px !important;
}

/* 오버라이드가 필요한 일부 vue3-draggable-resizable 스타일 */
:deep(.vdr-handle) {
  background-color: #4285F4 !important;
  border: 1px solid white !important;
  width: 12px !important;
  height: 12px !important;
  z-index: 200 !important;
}

:deep(.vdr-handle:hover) {
  background-color: #1a73e8 !important;
}

:deep(.vdr-stick) {
  z-index: 200 !important;
}

.qr-on-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  /* padding: 5px; */
  overflow: hidden;
  box-sizing: border-box;
}

.qr-image-on-canvas {
  /* max-width: 100%; */
  /* max-height: 100%; */
  width: 100%;
  height: 100%;
  /* object-fit: contain; */
  /* box-sizing: border-box; */
}

/* QR 코드 플레이스홀더 스타일 */
.qr-placeholder-on-canvas {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  box-sizing: border-box;
}

/* 엑셀 파일 업로드 및 배치 생성 관련 스타일 */
.excel-upload-container {
  background-color: #e8f5e9;
  border: 1px solid #c8e6c9;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  grid-column: 1 / -1; /* 전체 너비 차지 */
}

.excel-upload-title {
  font-size: 16px;
  font-weight: bold;
  color: #2e7d32;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.excel-upload-icon {
  font-size: 20px;
}

.excel-upload-box {
  border: 2px dashed #81c784;
  border-radius: 4px;
  padding: 25px;
  width: 100%;
  max-width: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s;
  background-color: rgba(129, 199, 132, 0.1);
}

.excel-upload-box:hover {
  background-color: rgba(129, 199, 132, 0.2);
  border-color: #4caf50;
}

.excel-input {
  display: none;
}

.excel-upload-info {
  margin-top: 15px;
  font-size: 14px;
  color: #555;
  text-align: center;
}

.excel-file-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.delete-excel-btn {
  background-color: #ff6b6b;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-excel-btn:hover {
  background-color: #ff5252;
}

.excel-filename-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #ffffff;
  border: 1px solid #c8e6c9;
  border-radius: 4px;
  gap: 8px;
  width: 100%;
  max-width: 450px;
}

.excel-file-icon {
  color: #2e7d32;
  font-size: 18px;
}

.excel-filename {
  flex-grow: 1;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.excel-remove-btn {
  background-color: transparent;
  border: none;
  color: #e53935;
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.excel-remove-btn:hover {
  color: #c62828;
}

/* QR 코드 배치 생성 결과 스타일 */
.qr-batch-results {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  grid-column: 1 / -1;
}

.qr-batch-results h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.batch-progress {
  margin-bottom: 20px;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #555;
}

.progress-bar-container {
  height: 16px;
  background-color: #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  border-radius: 8px;
  transition: width 0.3s ease;
}

.batch-error-message {
  margin-top: 15px;
  padding: 12px;
  background-color: #ffebee;
  border-left: 4px solid #f44336;
  color: #d32f2f;
  border-radius: 4px;
  font-weight: bold;
}

.batch-results-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #e8f5e9;
  border-radius: 4px;
}

.batch-item-list {
  margin-top: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.batch-list-header {
  display: grid;
  grid-template-columns: 50px 1fr 1fr 120px;
  background-color: #f5f5f5;
  padding: 10px;
  font-weight: bold;
  border-bottom: 1px solid #e0e0e0;
}

.batch-list-item {
  display: grid;
  grid-template-columns: 50px 1fr 1fr 120px;
  padding: 12px 10px;
  border-bottom: 1px solid #e0e0e0;
  align-items: center;
}

.batch-list-item:last-child {
  border-bottom: none;
}

.batch-item-number {
  text-align: center;
  font-weight: bold;
  color: #666;
}

.batch-item-name,
.batch-item-target {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 10px;
}

.batch-item-status {
  text-align: center;
  font-weight: bold;
}

.batch-status-success {
  color: #4caf50;
}

.batch-status-error {
  color: #f44336;
}

.batch-status-pending {
  color: #ff9800;
}

.batch-item-details {
  grid-column: 1 / -1;
  background-color: #f9f9f9;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
}

.batch-error-details {
  color: #f44336;
  font-style: italic;
  margin-top: 5px;
}

.batch-toggle-details-btn {
  background-color: transparent;
  border: none;
  color: #2196f3;
  cursor: pointer;
  font-size: 14px;
  padding: 0;
  text-decoration: underline;
}

.batch-expand-all {
  margin-bottom: 10px;
  text-align: right;
}

@media (max-width: 768px) {
  .batch-list-header,
  .batch-list-item {
    grid-template-columns: 40px 1fr 100px;
  }
  
  .batch-item-target {
    display: none;
  }
}
</style>
